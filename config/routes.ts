﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: '登录',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: '修改密码',
        path: '/user/change-password',
        component: './User/ChangePassword',
      },
    ],
  },
  {
    path: '/database',
    name: '数据库',
    icon: 'database',
    routes: [
      {
        path: '/database',
        redirect: '/database/list',
      },
      {
        path: '/database/list',
        component: './DataBase',
        hideInMenu: true,
      },
      {
        path: '/database/add',
        name: '新建',
        component: './DataBase/components/DataBaseForm',
        hideInMenu: true,
      },
      {
        path: '/database/edit/:id',
        name: '编辑',
        component: './DataBase/components/DataBaseForm',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/audit',
    name: '审计管理',
    icon: 'eye',
    routes: [
      {
        path: '/audit',
        redirect: '/audit/data-collect',
      },
      {
        path: '/audit/data-collect',
        name: '数据采集',
        component: './DataCollect',
      },
      {
        path: '/audit/data-audit',
        name: '变更审计',
        routes: [
          {
            path: '/audit/data-audit',
            redirect: '/audit/data-audit/data-audit-config',
          },
          {
            path: '/audit/data-audit/data-audit-config',
            name: '变更审计配置',
            component: './Audit/DataAudit/DataAuditConfig',
          },
          {
            path: '/audit/data-audit/data-audit-journal',
            name: '变更审计日志',
            component: './Audit/DataAudit/DataAuditJournal',
          },
          {
            path: '/audit/data-audit/data-audit-journal/data-audit-journal-detail',
            name: '详情',
            component: './Audit/DataAudit/DataAuditJournal/components/DataAuditJournalDetail',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/audit/journal-audit',
        name: '访问审计',
        routes: [
          {
            path: '/audit/journal-audit',
            redirect: '/audit/journal-audit/access-audit-journal',
          },
          {
            path: '/audit/journal-audit/access-audit-config',
            name: '访问审计配置',
            component: './Audit/AccessAudit/AccessAuditConfig',
          },
          {
            path: '/audit/journal-audit/access-audit-journal',
            name: '访问审计日志',
            component: './Audit/AccessAudit/AccessAuditJournal',
          },
        ],
      },
    ],
  },
  {
    path: '/data-desensitization',
    name: '脱敏管理',
    icon: 'dotChart',
    routes: [
      {
        path: '/data-desensitization',
        redirect: '/data-desensitization/policy',
      },
      {
        path: '/data-desensitization/policy',
        name: '识别规则',
        component: './DataDesensitization/Policy',
      },
      // {
      //   path: '/data-desensitization/dynamic-desensitization',
      //   name: '动态脱敏',
      //   component: './DataDesensitization/DynamicDesensitization',
      // },
      // {
      //   path: '/data-desensitization/manual-desensitization',
      //   name: '手动脱敏',
      //   component: './DataDesensitization/ManualDesensitization',
      // },
      {
        path: '/data-desensitization/manual-desensitization/manual-desensitization-details/:id',
        name: '详情',
        component:
          './DataDesensitization/ManualDesensitization/components/ManualDesensitizationDetails',
        hideInMenu: true,
      },
      {
        path: '/data-desensitization/scan-sensitive',
        name: '数据扫描',
        component: './DataDesensitization/ScanSensitive',
      },
      {
        path: '/data-desensitization/scan-sensitive/scan-sensitive-analysis/:id',
        name: '统计',
        component: './DataDesensitization/ScanSensitive/components/ScanSensitiveAnalysis',
        hideInMenu: true,
      },
      {
        path: '/data-desensitization/scan-sensitive/scan-sensitive-details/:id/:table',
        name: '详情',
        component: './DataDesensitization/ScanSensitive/components/ScanSensitiveDetails',
        hideInMenu: true,
      },
      {
        path: '/data-desensitization/mask-config',
        name: '数据脱敏',
        component: './DataDesensitization/MaskConfig',
      },
      {
        path: '/data-desensitization/mask-config/mask-config-details/:id',
        name: '详情',
        component: './DataDesensitization/MaskConfig/components/MaskConfigDetails',
        hideInMenu: true,
      },
      {
        path: '/data-desensitization/data-traceability',
        name: '数字水印',
        component: './DataDesensitization/DataTraceability',
      },
    ],
  },
  {
    path: '/data-synchronization',
    name: '实时数据同步',
    icon: 'sync',
    routes: [
      {
        path: '/data-synchronization',
        redirect: '/data-synchronization/list',
      },
      {
        path: '/data-synchronization/list',
        component: './DataSynchronization',
        hideInMenu: true,
      },
      {
        path: '/data-synchronization/sync-details',
        name: '详情',
        component: './DataSynchronization/components/SyncDetails',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/data-consume',
    name: '实时数据消费',
    icon: 'cloudSync',
    routes: [
      {
        path: '/data-consume',
        redirect: '/data-consume/server-collection',
      },
      {
        path: '/data-consume/server-collection',
        name: '服务器数据采集',
        component: './ServerCollection',
      },
      {
        path: '/data-consume/server-synchronous',
        component: './ServerSynchronous',
        name: '服务器数据同步',
      },
      {
        path: '/data-consume/server-synchronous/sync-details/:id',
        name: '详情',
        component: './ServerSynchronous/components/SyncDetails',
        hideInMenu: true,
      },
      {
        path: '/data-consume/database-primary-key-config',
        name: '数据库主键配置',
        component: './DatabasePrimaryKeyConfig',
      },
    ],
  },
  {
    path: '/sysLog',
    name: '日志',
    icon: 'profile',
    component: './SysLog',
  },
  {
    path: '/users',
    name: '用户',
    icon: 'user',
    component: './Users',
  },
  {
    path: '/system',
    name: '系统',
    icon: 'setting',
    component: './System',
  },
  {
    path: '/permissions',
    name: '角色与权限配置',
    icon: 'safetyCertificate',
    access: 'canViewPermissions',
    routes: [
      {
        path: '/permissions',
        redirect: '/permissions/role',
      },
      {
        path: '/permissions/role',
        name: '角色管理',
        routes: [
          {
            path: '/permissions/role',
            redirect: '/permissions/role/list',
          },
          {
            path: '/permissions/role/list',
            component: './Permissions/Roles',
            hideInMenu: true,
          },
          {
            path: '/permissions/role/config/add',
            name: '新建角色',
            component: './Permissions/Roles/components/RoleConfig',
            hideInMenu: true,
          },
          {
            path: '/permissions/role/config/edit/:id',
            name: '编辑',
            component: './Permissions/Roles/components/RoleConfig',
            hideInMenu: true,
          },
        ],
      },
      {
        path: '/permissions/feature',
        name: '功能管理',
        component: './Permissions/Feature',
      },
      {
        path: '/permissions/interface',
        name: '接口管理',
        routes: [
          {
            path: '/permissions/interface',
            redirect: '/permissions/interface/list',
          },
          {
            path: '/permissions/interface/list',
            component: './Permissions/Interface',
            hideInMenu: true,
          },
          {
            path: '/permissions/interface/config',
            name: '更新接口',
            component: './Permissions/Interface/components/InterFaceConfig',
            hideInMenu: true,
          },
        ],
      },
    ],
  },
  {
    path: '/approval',
    name: '审批管理',
    icon: 'audit',
    routes: [
      {
        path: '/approval',
        redirect: '/approval/dashboard',
      },
      {
        path: '/approval/dashboard',
        component: './Approval/Dashboard',
      },
      {
        path: '/approval/mask-config',
        name: '数据脱敏审批',
        component: './Approval/MaskConfig',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/',
    redirect: '/database',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
