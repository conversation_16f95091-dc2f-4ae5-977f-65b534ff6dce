stages:
  - npm-install
  - trigger
variables:
  VERSION: '$CI_COMMIT_REF_NAME-$CI_COMMIT_SHA'
  FRONTEND_BRANCH_NAME: $CI_COMMIT_REF_NAME
  TOKEN: glptt-948399f3e064a917d11c301e4c0b48e0ec0a740b
  SERVER_PROJECT_ID: 48
include:
  - project: 'develop/devops-projects/ci-template'
    ref: main
    file: 'yarn-cache-build.gitlab-ci.yml'
  - project: 'develop/devops-projects/ci-template'
    ref: main
    file: 'npm-trigger.gitlab-ci.yml'
