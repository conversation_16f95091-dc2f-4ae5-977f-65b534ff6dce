import { loginUsingPOST } from '@/services/dsp/userController';
import { history, useRequest } from '@umijs/max';
import { Spin, Typography } from 'antd';
import { FC } from 'react';
import styles from './index.less';

const AutoLogin: FC<{
  account: string;
  password: string;
}> = ({ account, password }) => {
  useRequest(
    () =>
      loginUsingPOST({
        userName: account,
        password,
      }),
    {
      pollingInterval: 5000,
      formatResult: (res) => res,
      onSuccess: (res) => {
        if (res.code === 100) {
          localStorage.setItem('RKLINK_DSP_TOKEN', res.data?.token || '');
          history.replace('/');
          window.location.reload();
        }
      },
    },
  );
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Spin spinning size="large" style={{ marginBlockEnd: 16 }} />
        <Typography.Title level={5}>自动登录中</Typography.Title>
        <Typography.Text>加载中，请耐心等待。</Typography.Text>
      </div>
    </div>
  );
};

export default AutoLogin;
