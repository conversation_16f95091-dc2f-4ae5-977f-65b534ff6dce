import { Typography } from 'antd';
import React from 'react';

const { Text } = Typography;

const RKSelectLabel: React.FC<{
  title?: string;
  info?: string;
}> = ({ title = '', info = '' }) => {
  return (
    <div>
      <Text strong>{title}</Text>
      <br />
      <Text type="secondary" style={{ whiteSpace: 'normal' }}>
        {info}
      </Text>
    </div>
  );
};

export default RKSelectLabel;
