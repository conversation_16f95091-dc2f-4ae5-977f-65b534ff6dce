import { getAbleStrategyUsingGET } from '@/services/dsp/strategyController';
import {
  ActionType,
  EditableProTable,
  ProColumns,
  RowEditableConfig,
} from '@ant-design/pro-components';
import { useRef, useState } from 'react';

type Strategy = { columnsName: string; codeName?: string; schema_tableName?: string };
type PolicyTableProps = {
  defaultData: Strategy[];
};

const PolicyTable: React.FC<RowEditableConfig<Strategy> & PolicyTableProps> = ({
  defaultData,
  onValuesChange,
}) => {
  const tableRef = useRef<ActionType | undefined>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(
    () => defaultData.map((item: { columnsName: string }) => item.columnsName) as string[],
  );
  const [dataSource, setDataSource] = useState<readonly Strategy[]>(() => defaultData);

  // 表格
  const columns: ProColumns<Strategy>[] = [
    {
      title: '字段',
      dataIndex: 'columnsName',
      editable: () => {
        return false;
      },
    },
    {
      title: '策略',
      dataIndex: 'codeName',
      valueType: 'select',
      request: async () => {
        const res = await getAbleStrategyUsingGET();
        const data = res.data || [];
        const options = data.map((item: { strategyName: string; id: number; codeName: string }) => {
          return {
            label: item.strategyName,
            value: item.codeName,
          };
        });
        return options;
      },
    },
  ];
  return (
    <EditableProTable<Strategy>
      rowKey="columnsName"
      actionRef={tableRef}
      search={false}
      options={false}
      columns={columns}
      pagination={false}
      recordCreatorProps={false}
      value={dataSource}
      onChange={setDataSource}
      size="small"
      editable={{
        type: 'multiple',
        editableKeys,
        onChange: setEditableRowKeys,
        onValuesChange: (record, dataSource) => {
          setDataSource(dataSource);
          onValuesChange?.(record, dataSource);
        },
      }}
    />
  );
};

export default PolicyTable;
