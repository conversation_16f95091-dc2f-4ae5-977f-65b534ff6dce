import { getStrategyList } from '@/pages/DataDesensitization/ManualDesensitization/components/ManualDesensitizationDrawerForm';
import {
  fetchColumnsUsingGET,
  fetchSchemasUsingGET,
  fetchTablesUsingGET,
} from '@/services/dsp/syncConfigController';
import { RowEditableConfig } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Checkbox, Collapse, CollapseProps, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import PolicyTable from './PolicyTable';

type SchemaTableColumnProps = {
  dbId?: number;
  initCheckedKeys?: any;
  originCheckedKeys?: string[]; //采集配置中已选择的数组
  onSaveChange: (key?: string, tableSource?: any) => void;
  onCheckChange?: (checkedKeys?: any) => void;
};
type Strategy = { columnsName: string; codeName?: string; schema_tableName?: string };

export const getStrategyObj = (
  tableList: { columnsName: string; codeName?: string; schema_tableName?: string }[],
) => {
  let strategyObj: { [key: string]: string } = {};

  tableList?.forEach((item) => {
    strategyObj[item.columnsName] = item.codeName!;
  });
  return strategyObj;
};

const SchemaTableColumnTree: React.FC<RowEditableConfig<Strategy> & SchemaTableColumnProps> = ({
  dbId,
  originCheckedKeys,
  initCheckedKeys,
  onSaveChange,
  onCheckChange,
}) => {
  const treeRef = useRef<
    {
      key: string;
      title: string;
      children?: {
        key: string;
        title: string;
        strategyList: { columnsName: string; codeName?: string }[];
      }[];
    }[]
  >([]);

  const checkedKeysRef = useRef<{ [key: string]: { [key: string]: string } }>(
    initCheckedKeys || {},
  );
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  //获取schema
  const { run: querySchemas, loading: schemaLoading } = useRequest(
    (dbId) => fetchSchemasUsingGET({ dbId }, { timeout: 5 * 60 * 1000 }),
    {
      manual: true,
      onSuccess: (data) => {
        const schemas = data?.map((item: { schema: string }) => ({
          key: item.schema,
          title: item.schema,
          children: [],
        }));
        treeRef.current = schemas;
        //筛选
        if (originCheckedKeys) {
          treeRef.current = schemas.filter((item: { key: string }) =>
            originCheckedKeys.includes(item.key),
          );
        }
      },
    },
  );
  useEffect(() => {
    if (dbId) {
      querySchemas(dbId);
    }
  }, [dbId]);

  useEffect(() => {
    onCheckChange?.(checkedKeysRef.current);
    setCheckedKeys(Object.keys(checkedKeysRef.current));
  }, [checkedKeysRef.current]);
  //获取tableS
  const { run: queryTables, loading: tableLoading } = useRequest(
    ({ dbId, schemaName }) => fetchTablesUsingGET({ dbId, schemaName }, { timeout: 5 * 60 * 1000 }),
    {
      manual: true,
      onSuccess: (data, params) => {
        const schemaName = params.at(0).schemaName;
        const tables = data?.map((item: { table_name: string }) => ({
          key: schemaName + '.' + item.table_name,
          title: item.table_name,
        }));
        treeRef.current = treeRef.current.map((item) => {
          if (item.key === schemaName) {
            //存在则筛选
            if (originCheckedKeys) {
              return {
                ...item,
                children: tables.filter((item: { key: string }) =>
                  originCheckedKeys.includes(item.key),
                ),
              };
            } else {
              return { ...item, children: tables };
            }
          }
          return item;
        });
      },
    },
  );

  const onSchemaChange: CollapseProps['onChange'] = (key) => {
    const schemaName = [...key].pop();
    if (!schemaName) return;
    if (schemaName) queryTables({ dbId: dbId, schemaName: schemaName });
  };

  const { run: queryColumns, loading: columnsLoading } = useRequest(
    (dbId, tableName) => fetchColumnsUsingGET({ dbId, tableName }, { timeout: 5 * 60 * 1000 }),
    {
      manual: true,
      onSuccess: (data, params) => {
        const schema_tableName = params[1];
        const schemaName = schema_tableName.split('.')[0];

        const strategyList = getStrategyList(data as { [key: string]: string })?.map((item) => {
          if (initCheckedKeys) {
            Object.entries(initCheckedKeys)?.forEach(([key, value]) => {
              if (key === schema_tableName) {
                getStrategyList(value as { [key: string]: string }).forEach((item2) => {
                  if (item2.columnsName === item.columnsName) {
                    item.codeName = item2.codeName;
                  }
                });
              }
            });
          }
          return {
            ...item,
            schema_tableName: schema_tableName,
          };
        });

        treeRef.current = treeRef.current.map((item) => {
          if (item.key === schemaName) {
            item.children?.map(
              (item1: {
                strategyList: { columnsName: string; codeName?: string }[];
                key: string;
              }) => {
                if (item1.key === schema_tableName) {
                  item1.strategyList = strategyList;
                }
                return item1;
              },
            );
          }
          return item;
        });
        let obj: { [key: string]: { [key: string]: string } } = {};
        obj[schema_tableName] = data!;
        checkedKeysRef.current = Object.assign(checkedKeysRef.current, obj);
        setCheckedKeys(Object.keys(checkedKeysRef.current));
      },
    },
  );

  const onTableChange = (
    key: string | string[],
    strategyList: { columnsName: string; codeName?: string | undefined }[] | undefined,
  ) => {
    const tableName = [...key].pop();
    if (!tableName) return;
    if (tableName && strategyList === undefined) queryColumns(dbId, tableName);
  };

  const checkedChange = (
    e: any,
    strategyList: { columnsName: string; codeName?: string | undefined }[],
  ) => {
    const { value, checked } = e.target;

    if (checked === false) {
      checkedKeysRef.current = Object.fromEntries(
        Object.entries(checkedKeysRef.current).filter((item) => item[0] !== value),
      );
      setCheckedKeys(Object.keys(checkedKeysRef.current));
    } else {
      let obj: { [key: string]: { [key: string]: string } } = {};
      obj[value] = getStrategyObj(strategyList);
      checkedKeysRef.current = Object.assign(checkedKeysRef.current, obj);
      setCheckedKeys(Object.keys(checkedKeysRef.current));
      onTableChange([value], undefined);
    }
  };

  return (
    <Spin spinning={schemaLoading || tableLoading || columnsLoading}>
      {treeRef.current?.map((item) => {
        return (
          <Collapse
            ghost
            onChange={(key) => onSchemaChange(key)}
            defaultActiveKey={[]}
            collapsible="header"
            key={item.key}
          >
            <Collapse.Panel header={item.title} key={item.key}>
              {item.children?.map((item1) => (
                <Collapse
                  key={item1.key}
                  ghost
                  onChange={(key) => onTableChange(key, item1.strategyList)}
                  defaultActiveKey={[]}
                  collapsible="header"
                >
                  <Collapse.Panel
                    header={
                      <>
                        <Checkbox
                          onChange={(e) => checkedChange(e, item1.strategyList)}
                          value={item1.key}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          checked={checkedKeys?.includes(item1.key)}
                        />

                        <span style={{ marginLeft: '5px' }}>{item1.title}</span>
                      </>
                    }
                    key={item1.key}
                  >
                    {item1.strategyList && (
                      <PolicyTable
                        defaultData={item1.strategyList}
                        onValuesChange={(record, dataSource) => {
                          const { schema_tableName } = record;
                          const obj = getStrategyObj(dataSource);
                          onSaveChange?.(schema_tableName, obj);
                        }}
                      />
                    )}
                  </Collapse.Panel>
                </Collapse>
              ))}
            </Collapse.Panel>
          </Collapse>
        );
      })}
    </Spin>
  );
};

export default SchemaTableColumnTree;
