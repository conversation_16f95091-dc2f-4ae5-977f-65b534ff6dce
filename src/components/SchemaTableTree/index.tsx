import { fetchSchemasUsingGET, fetchTablesUsingGET } from '@/services/dsp/syncConfigController';
import { excColumnReg } from '@/utils/validator';
import { ProFormTextArea } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Spin, Tree } from 'antd';
import { DataNode, TreeProps } from 'antd/es/tree';
import { Key, useEffect, useMemo, useRef, useState } from 'react';

type SchemaTableProps = {
  dbId: number;
  initCheckedKeys?: TreeProps['checkedKeys']; //选择的数组
  originCheckedKeys?: string[]; //采集配置中已选择的数组
  isShowSearch?: boolean; //是否展示排除字段
};

const SchemaTableTree: React.FC<TreeProps & SchemaTableProps> = ({
  isShowSearch = true,
  dbId,
  initCheckedKeys,
  onCheck,
  originCheckedKeys,
}) => {
  const defaultDataRef = useRef<DataNode[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<TreeProps['checkedKeys']>(initCheckedKeys);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [loadedKeys, setLoadedKeys] = useState<Key[]>();

  //获取数据库
  const { run: getSchemas, loading } = useRequest(
    (dbId) => fetchSchemasUsingGET({ dbId }, { timeout: 5 * 60 * 1000 }),
    {
      onSuccess: (data) => {
        const schemas = data?.map((item: { schema: string }) => {
          return { key: item.schema, title: item.schema, isLeaf: false, children: undefined };
        });
        defaultDataRef.current = schemas;
        //筛选
        if (originCheckedKeys) {
          defaultDataRef.current = schemas.filter((item: { key: string }) =>
            originCheckedKeys.includes(item.key),
          );
        }
      },
      manual: true,
    },
  );
  useEffect(() => {
    if (dbId) {
      defaultDataRef.current = [];
      setExpandedKeys([]);
      getSchemas(dbId);
    }
    //新建
    if (originCheckedKeys && !initCheckedKeys) {
      setCheckedKeys({ halfChecked: [], checked: originCheckedKeys });
    }
    //编辑
    if (originCheckedKeys && initCheckedKeys) {
      const originTableNameList = originCheckedKeys.filter((item) => item.indexOf('.') !== -1);
      const initTableNameList = (initCheckedKeys as { checked: Key[]; halfChecked: Key[] }).checked;
      //判断是否为全选
      if (originTableNameList.length === initTableNameList.length) {
        setCheckedKeys({ halfChecked: [], checked: originCheckedKeys });
      }
    }
  }, [dbId, originCheckedKeys, initCheckedKeys]);

  //获取表
  const { run: getTables } = useRequest(
    ({ dbId, schemaName }) => fetchTablesUsingGET({ dbId, schemaName }, { timeout: 5 * 60 * 1000 }),
    {
      manual: true,
      onSuccess: (data, params) => {
        const schemaName = params.at(0).schemaName;
        const tables = data?.map((item: { table_name: string }) => {
          return {
            key: schemaName + '.' + item.table_name,
            title: item.table_name,
            isLeaf: true,
          };
        });
        defaultDataRef.current = defaultDataRef.current.map((item) => {
          if (item.key === schemaName) {
            //存在则筛选
            if (originCheckedKeys) {
              return {
                ...item,
                children: tables.filter((item: { key: Key }) =>
                  originCheckedKeys.includes(item.key as string),
                ),
              };
            } else {
              return { ...item, children: tables };
            }
          }
          return item;
        });
      },
    },
  );

  //异步加载
  const onLoadData = async ({ key, children }: any) => {
    if (children) {
      return;
    }

    return getTables({ dbId: dbId, schemaName: key });
  };

  const onExpand: TreeProps['onExpand'] = (newExpandedKeys) => {
    //当前节点收起
    if (expandedKeys.length > newExpandedKeys.length) {
      const newLoadedKeys = loadedKeys?.filter((i) => newExpandedKeys.includes(i));
      setLoadedKeys(newLoadedKeys);
    }
    setExpandedKeys(newExpandedKeys);
  };
  const onLoad = (loadedKeys: Key[]) => {
    setLoadedKeys(loadedKeys);
  };

  const treeData = useMemo(() => {
    const loop = (data: any[] | undefined): any =>
      data?.map((item) => {
        const strTitle = item.title as string;
        if (item.children) {
          const children = loop(item.children).map((child: any) => {
            return { ...child, isLeaf: true };
          });
          return { title: strTitle, key: item.key, children: children };
        }

        return {
          title: strTitle,
          key: item.key,
          isLeaf: false,
          children: undefined,
        };
      });
    return loop(defaultDataRef.current);
  }, [defaultDataRef.current]);

  /**
   * 处理半选全选
   * 1.父节点勾选子节点也要全部勾选
   * 2.父节点取消子节点全部取消
   * 3.子节点全部勾选,父节点勾选
   *  4.子节点取消父节点半选
   * @param checked
   * @param info
   */
  const handleCheck: TreeProps['onCheck'] = (checked, info) => {
    const checkedValue = checked as { checked: Key[]; halfChecked: Key[] };
    //半选数组
    let halfList = Array.from(
      new Set((checkedValue.checked as string[]).map((item) => item.split('.')[0])),
    );
    let checkedChange = [...checkedValue.checked] as string[];
    // 父节点
    if ((info.node.key as string).indexOf('.') === -1) {
      if (info.checked === true) {
        //全选
        if (info.node.children) {
          const keyList = info.node.children?.map((item) => item.key);
          checkedChange = [...checkedChange, ...(keyList as string[])];
          halfList = halfList.filter((item) => item !== info.node.key);
        }
      } else {
        //取消全选
        checkedChange = checkedChange.filter(
          (checked) => checked.indexOf((info.node.key as string) + '.') === -1,
        );
        halfList = halfList.filter((item) => item !== info.node.key);
      }
    } else {
      //子节点
      //子节点全选时父节点也要选择
      if (info.checked === true) {
        const schemaName = (info.node.key as string).split('.')[0];
        treeData.forEach((item: { children: any; key: string; title: string }) => {
          if (item.key === schemaName) {
            // 该父节点下的子数组
            const itemChild = item.children?.map((child: { key: string }) => child.key);
            //已选择数组中的包含该父节点的key值数组
            const changeList = checkedChange.filter(
              (checked) => checked.indexOf(schemaName + '.') !== -1,
            );
            if (changeList.length === itemChild.length) {
              checkedChange = [...checkedChange, item.key];
              halfList = halfList.filter((halfChecked) => halfChecked !== item.key);
            }
          }
        });
      } else {
        //取消勾选
        checkedChange = checkedChange.filter((checked) => checked.indexOf('.') !== -1);
        info.checkedNodes = info.checkedNodes.filter((checked) => !checked.children);
      }
    }
    //checkedChange为空时父节点也为空
    if (checkedChange.length === 0) {
      halfList = [];
    }
    setCheckedKeys({ checked: checkedChange, halfChecked: halfList });
    onCheck?.({ checked: checkedChange, halfChecked: halfList }, info);
  };

  const excludeColumnDisabled =
    (
      checkedKeys as {
        checked: Key[];
        halfChecked: Key[];
      }
    )?.halfChecked.length > 0 ||
    (
      checkedKeys as {
        checked: Key[];
        halfChecked: Key[];
      }
    )?.checked.length > 0;

  return (
    <div>
      <Spin spinning={loading}>
        {isShowSearch === true && (
          <ProFormTextArea
            name="excludeColumn"
            label="排除字段"
            placeholder="请输入排除字段"
            disabled={!excludeColumnDisabled}
            tooltip="输入排除字段，格式为<schema_name>.<table_name>.<column_name>，请用逗号分隔"
            rules={[
              {
                pattern: excColumnReg,
                message: '格式必须为<schema_name>.<table_name>.<column_name>,多个排除字段请用,分割',
              },
            ]}
            fieldProps={{
              autoSize: {
                minRows: 1,
                maxRows: 2,
              },
            }}
          />
        )}

        <Tree
          checkable
          blockNode
          loadData={onLoadData}
          treeData={treeData}
          onCheck={handleCheck}
          checkedKeys={checkedKeys}
          checkStrictly={true}
          onExpand={onExpand}
          expandedKeys={expandedKeys}
          onLoad={onLoad}
          loadedKeys={loadedKeys}
        />
      </Spin>
    </div>
  );
};

export default SchemaTableTree;
