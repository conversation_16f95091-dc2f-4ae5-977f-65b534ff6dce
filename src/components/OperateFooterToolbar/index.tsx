import { FooterToolbar } from '@ant-design/pro-components';
import { Button } from 'antd';
import React, { Key, useMemo } from 'react';

type Action = {
  key: string;
  label: string;
};

type ToolBarProps = {
  selectedRows: any[];
  actions?: Action[];
  onDelete?: (selectedRows: any[], selectedKeys: Key[]) => void;
  onOperation?: (key: string, selectedRows: any[], selectedKeys: Key[]) => void;
};

const OperateFooterToolbar: React.FC<ToolBarProps> = ({
  selectedRows,
  onDelete,
  actions,
  onOperation,
}) => {
  const ActionsBtn = useMemo(() => {
    return actions?.map((item) => {
      return (
        <Button
          key={item.key}
          type="primary"
          onClick={() =>
            onOperation?.(item.key, selectedRows, selectedRows?.map((item) => item?.id) || [])
          }
        >
          {item.label}
        </Button>
      );
    });
  }, [actions, selectedRows]);

  return (
    <>
      {selectedRows.length ? (
        <FooterToolbar
          extra={
            <div>
              已选择{' '}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRows.length}
              </a>{' '}
              项
            </div>
          }
        >
          {onDelete && (
            <Button
              type="primary"
              onClick={() => onDelete?.(selectedRows, selectedRows?.map((item) => item?.id) || [])}
            >
              批量删除
            </Button>
          )}

          {ActionsBtn}
        </FooterToolbar>
      ) : null}
    </>
  );
};

export default OperateFooterToolbar;
