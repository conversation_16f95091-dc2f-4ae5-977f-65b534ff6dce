import { createContext } from 'react';

type BaseListContextProps = {
  dbList?: API.DB[];
  dbLoading?: boolean;
  listenerList?: API.DataListenerConfig[];
  listenerLoading?: boolean;
  serverCollectionList?: API.DataCollection[];
  serverCollectionLoading?: boolean;
  roleList?: API.Role[];
  roleLoading?: boolean;
  featureTreeList?: Record<string, any>[];
  featureTreeLoading?: boolean;
};

const BaseListContext = createContext<BaseListContextProps>({});

export default BaseListContext;
