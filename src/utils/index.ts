import { RequestOptionsType } from '@ant-design/pro-components';
import { message } from 'antd';
import * as CryptoJS from 'crypto-js';
import dayjs from 'dayjs';
import { Key, SyntheticEvent } from 'react';
/**
 * 取消事件冒泡
 * @param e
 */
export function cancelBubble(e: SyntheticEvent) {
  e.stopPropagation();
  e.nativeEvent.stopImmediatePropagation();
}

export function renderEmpty(value: string | number | undefined) {
  return value ?? '-';
}

/**
 * Key[] 转antd option
 * @param arr Key[]
 * @returns option
 */

export const getOptions = (arr?: Key[]): RequestOptionsType[] => {
  if (!arr || !arr.length) return [];
  return arr.map((item) => ({
    value: item,
    label: item,
  }));
};

/**
 * 获取表格参数
 * @param 表格参数
 * @returns 分页表格所需参数
 */

export const getTableParams = (params: Record<string, any>): BaseListRequest => {
  const { current, pageSize, restProps } = params;
  return {
    pageNum: current,
    pageSize,
    ...restProps,
  };
};

// 接口修改、新增成功 回调返回上一级
export const onSuccessAndGoBack = (res: API.Result) => {
  if (res.code === 100) {
    message.success('操作成功！');
    history.go(-1);
  }
};

// option2enum
export const option2enum = (options: { value: React.Key; label: string }[]) => {
  const obj: Record<string, any> = {};
  options.forEach((item) => {
    const { value, label } = item;
    obj[value] = { text: label, ...item };
  });
  return obj;
};

/**
 * 字符串、int转时间
 * @param dateString、dateInt
 * @returns
 */

export const formatDate = (text: string | number) => {
  if (!text) return '-';
  return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
};

export const queryPagingTable = async <U>(
  params: AntTableParams & { [k: string]: any },
  api: (data: U) => Promise<Record<string, any>>,
) => {
  const { current, pageSize, ...restProps } = params;
  const data = {
    pageNum: current,
    pageSize,
    ...restProps,
  };
  const msg = await api(data as U);
  return {
    data: msg?.data?.records || [],
    success: true,
    total: msg?.data?.total,
  };
};

/**
 * 将'schemaName.tableName,schema-changes.abcd'转换为
 * ['schemaName.tableName','schema-changes.abcd','schemaName','schema-changes']
 * @param tableNames
 * @returns
 */
export const tableNamesTransform = (tableNames?: string) => {
  const tableNameList: string[] = tableNames?.split(',') || [];
  const schemaList = Array.from(new Set(tableNameList.map((item) => item.split('.')[0])));
  const originCheckedKeys = [...tableNameList, ...schemaList];
  return originCheckedKeys;
};
/**
 *将'schemaName.tableName,schema-changes.abcd'转换为
 {checked:['schemaName.tableName','schema-changes.abcd'],halfChecked:['schemaName','schema-changes']}
 * @param tables
 * @returns
 */
export const tablesTransform = (tables?: string) => {
  const initTableNameList: string[] = tables?.split(',') || [];
  const initSchemaList = Array.from(new Set(initTableNameList.map((item) => item.split('.')[0])));
  const initialCheckedKey = {
    checked: initTableNameList,
    halfChecked: initSchemaList,
  };
  return initialCheckedKey;
};

/**
 *为了减少接口请求因此从表格获取全部数据，并将数据存储到sessionStorage
 * @param locationState
 * @returns
 */
export const editPageData = (locationState: Record<string, any>) => {
  let state: Record<string, any>;
  if (locationState) {
    state = locationState;
    sessionStorage.setItem('urlData', JSON.stringify(state));
  } else {
    state = JSON.parse(sessionStorage.getItem('urlData')!);
  }
  return state;
};

export const encryptionKey = 'rkLink_login';

/**
 * 获取指定名称的cookie值。
 * @param name 需要获取的cookie的名称。
 * @returns 如果找到指定名称的cookie，则返回其值；如果找不到，则返回null。
 */
export const getCredentialsCookie = () => {
  const cookieArr = document.cookie.split('; ');

  for (let i = 0; i < cookieArr.length; i++) {
    const cookiePair = cookieArr[i].split('=');

    // 注意，如果Cookie没有值，它会以等号开始
    if (decodeURIComponent(cookiePair[0]) === 'credentials') {
      const info = JSON.parse(decodeURIComponent(cookiePair[1] || ''));

      try {
        const decryptedPassword = CryptoJS.AES.decrypt(info.password, encryptionKey).toString(
          CryptoJS.enc.Utf8,
        );
        info.password = decryptedPassword;
        return info;
      } catch (error) {
        console.error('Error decrypting cookie:', error);
        return null;
      }
    }
  }

  return null; // 如果找不到匹配的Cookie，返回null
};

/**
 * 获取form数据
 * @param params 参数
 * @param ready 是否请求
 * @param api 接口名称
 * @returns
 */

export const queryFormData = async <U>(
  params: { [k: string]: any },
  ready: boolean,
  api?: (data: U) => Promise<Record<string, any>>,
) => {
  if (!ready) return {};
  if (!api) return {};
  const msg = await api(params as U);
  if (msg.code === 100) {
    return msg.data;
  }
  return {};
};
/**
 *  获取随机id
 */
export const getRandomId = () => {
  return (Math.random() * 1000000).toFixed(0);
};

/**
 * 将tree转换为一维数组
 */
export const transformTreeToArray = (treeList: Record<string, any>[]) => {
  const result: Record<string, any>[] = [];
  const traverse = (node: Record<string, any>) => {
    result.push(node);
    if (node.children && node.children.length > 0) {
      node.children.forEach((it: Record<string, any>) => traverse(it));
    }
  };
  treeList.forEach((item) => traverse(item));
  return result;
};

/**
 * 将功能权限的map数据转换为tree
 * @param obj
 * @returns
 */
export const transformFeatureMapToTree = (obj: Record<string, any>) => {
  return Object.entries(obj).map(([key, value]) => {
    const match = key?.match(/\(([^)]+)\)/);
    if (!match) return {};
    const arr = match?.[1].split(', ');
    const arr1 = arr.map((i) => {
      const [key, value] = i.split('=');
      return [key, value === 'null' ? null : value];
    });
    const item = Object.fromEntries(arr1) as Record<string, any>;
    return {
      ...item,
      children: (value as Record<string, any>[]).map((it) => ({
        ...it,
        newName: `${item.moduleName}-${it.operationType}`,
        key: it.id,
        title: it.operationType,
      })),
      newName: item.moduleName,
      key: parseInt(item.id),
      title: item.moduleName,
    };
  });
};
