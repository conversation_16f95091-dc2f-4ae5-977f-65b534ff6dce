// 手机号校验
export const phoneReg = /^1[3|4|5|7|8][0-9]{9}$/;

// 密码
export const passwordReg = /(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,}/;

// 名称
export const nameReg = /[a-zA-Z\u4e00-\u9fa5]/;

// ip
export const IPReg =
  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;

// port
export const PortReg = /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;

//ip+端口或ip，以逗号分隔
export const RacNodeReg = /^(?:(?:\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(?::\d{1,5})?)(?:,\s*|$))+$/;

//schema_name.table_name.column_name
export const excColumnReg = /^(?:(?:([\w_]+\.[\w_]+\.[\w_]+))(?:,\s*|$))+$/;

//ip+端口，以逗号分隔
export const BootstrapServersReg =
  /^(?:(?:\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}?)(?:,\s*|$))+$/;

// 数据库名.表名，多个字段以逗号分隔
export const TableNamesReg = /^(?:(?:([\w_]+\.[\w_]+))(?:,\s*|$))+$/;

// 主题名称字符串+数字组合，多个字段以逗号分隔
export const topicNamesReg = /^(?:[^,]+)(?:,\s*[^,]+)*$/;
