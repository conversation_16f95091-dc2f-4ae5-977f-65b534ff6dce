// 清理条件
export const ROLE = [
  { label: '开发', value: 'developer' },
  { label: '测试', value: 'tester' },
  { label: '管理员', value: 'master' },
];

// 用户状态
export const USER_STATUS = [
  { label: '启用', value: 0, status: 'success' },
  { label: '停止', value: 1, status: 'error' },
];

// 启停状态
export const EXEC_STATUS = [
  { label: '运行', value: 0, status: 'success' },
  { label: '停止', value: 1, status: 'error' },
];

//环境类型
export const ENVIRONMENT_TYPE = [
  { label: '生产环境', value: '生产环境' },
  { label: '开发环境', value: '开发环境' },
  { label: '测试环境', value: '测试环境' },
];

// 手动脱敏当前状态
export const MANUAL_STATUS = [
  { label: '执行中', value: 0, status: 'processing' },
  { label: '未执行', value: 1, status: 'default' },
];
//手动脱敏最后状态
export const MANUAL_LAST_STATUS = [
  { label: '执行中', value: 0, status: 'processing' },
  { label: '成功', value: 1, status: 'success' },
  { label: '失败', value: 2, status: 'error' },
  { label: '未执行', value: 3, status: 'default' },
];

//日志-执行结果
export const EXECUTION_STATUS = [
  { label: '操作成功', value: '100' },
  { label: '操作失败', value: '500' },
  { label: '令牌过期', value: '401' },
  { label: '无权限', value: '403' },
  { label: '资源过期', value: '410' },
];

//数据扫描-类型
export const SCAN_TYPE = [
  { label: '文件', value: 'file' },
  { label: 'SQL', value: 'sql' },
  { label: '数据库', value: 'db' },
];

//数据脱敏两种状态
export const MASK_CONFIG_STATUS = [
  { label: '执行中', value: '0', status: 'processing' },
  { label: '已完成', value: '1', status: 'success' },
  { label: '失败', value: '2', status: 'error' },
  { label: '未执行', value: '3', status: 'default' },
  { label: '中止', value: '4', status: 'Warning' },
];

//脱敏策略枚举
export const MASK_RULE_TYPES = [
  { label: '保留格式脱敏', value: 'SIMULATE' },
  { label: '掩盖脱敏', value: 'COVER' },
  { label: 'Hash脱敏', value: 'HASH' },
  { label: '取整脱敏', value: 'ROUND' },
  { label: '置空脱敏', value: 'BLANK' },
];

// 用户状态
export const POLICY_STATUS = [
  { label: '自定义规则', value: 0 },
  { label: '默认规则', value: 1 },
];

//接口配置中请求方式颜色
export const INTERFACE_METHODS_COLOR: Record<string, any> = {
  GET: 'green',
  POST: 'volcano',
  PUT: 'blue',
  DELETE: 'red',
  PATCH: 'magenta',
  OPTIONS: 'blue',
};

/**
 * 审批状态
 *
 */
export const APPROVAL_STATUS = [
  { label: '已通过', value: '1', status: 'success' },
  { label: '已拒绝', value: '2', status: 'error' },
  { label: '待审批', value: '0', status: 'processing' },
];
