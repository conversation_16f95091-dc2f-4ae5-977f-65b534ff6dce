import { useEmotionCss } from '@ant-design/use-emotion-css';
import { history } from '@umijs/max';
import { Button, Result } from 'antd';
import React from 'react';

const NoFoundPage: React.FC = () => {
  const resultClassName = useEmotionCss(() => {
    return {
      height: '100%',
      width: '100%',
      padding: 0,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      img: {
        width: '30vw',
        minWidth: 375,
      },
    };
  });
  return (
    <Result
      className={resultClassName}
      // title="404"
      icon={<img src="/images/404.webp" />}
      subTitle="对不起，您访问的页面不存在。"
      extra={
        <Button type="primary" onClick={() => history.push('/')}>
          Back Home
        </Button>
      }
    />
  );
};

export default NoFoundPage;
