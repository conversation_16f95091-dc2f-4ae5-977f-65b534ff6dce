import { APPROVAL_STATUS } from '@/enums';
import {
  getAllApprovalListUsingGET,
  modifyApprovalListUsingPOST,
} from '@/services/dsp/approvalListController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useModel, useRequest } from '@umijs/max';
import { Button, Form, message, Modal, Space } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React, { useRef } from 'react';

const MaskConfig: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  //获取当前用户
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [form] = useForm();
  //通过或驳回
  const { run: updateApprovalStatus } = useRequest((val) => modifyApprovalListUsingPOST(val), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 100) return;
      form.resetFields();
      message.success('操作成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
    onError: (err) => {
      form.resetFields();
      console.log('🚀 ~ const{run:updateApprovalStatus}=useRequest ~ err:', err);
    },
  });

  const handlePass = async (record: API.ApprovalList) => {
    updateApprovalStatus({
      ...record,
      approvalStatus: 1,
    });
  };
  const handleOver = async (record: API.ApprovalList) => {
    Modal.confirm({
      title: '确认驳回',
      content: (
        <Form form={form}>
          <ProFormTextArea
            width="md"
            name="description"
            label="描述"
            placeholder="请输入"
            fieldProps={{ autoSize: { minRows: 1, maxRows: 2 } }}
          />
        </Form>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const description = form.getFieldValue('description');
        updateApprovalStatus({
          ...record,
          approvalStatus: 2,
          description,
          auditorId: currentUser?.id,
        });
      },
      onCancel: () => {
        form.resetFields();
      },
    });
  };

  // 表格
  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '名称',
      dataIndex: 'maskConfigName',
      // width: 150,
      ellipsis: true,
      fixed: 'left',
    },
    {
      title: '提交人',
      dataIndex: 'listerName',
      // width: 180,
      ellipsis: true,
    },
    {
      title: '审批人',
      dataIndex: 'auditorName',
      // width: 150,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      // width: 150,
      ellipsis: true,
    },
    {
      title: '审批状态',
      dataIndex: 'approvalStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      // width: 100,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { auditorId, approvalStatus } = record;
        //审批人为当前用户同时审批状态为待审批
        const isPassOrOver = auditorId === currentUser?.id && approvalStatus === 0;
        return (
          <Space>
            {isPassOrOver && (
              <Space>
                <Button
                  key="pass"
                  type="link"
                  className="ant-btn-link-table"
                  onClick={() => handlePass(record)}
                >
                  通过
                </Button>
                <Button
                  key="over"
                  type="link"
                  className="ant-btn-link-table"
                  onClick={() => handleOver(record)}
                >
                  驳回
                </Button>
              </Space>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.MaskConfig>
        {...defaultTableConfig}
        search={false}
        scroll={{ x: '100%' }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="审批列表"
        request={async (params) => {
          const { current, pageSize } = params;
          return queryPagingTable(
            {
              pageNum: current,
              pageSize,
              userId: currentUser?.id,
            },
            getAllApprovalListUsingGET,
          );
        }}
      />
    </PageContainer>
  );
};

export default MaskConfig;
