import { PageContainer, ProCard } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Col, Row, Typography } from 'antd';
const colSpan = { xs: 12, sm: 12, md: 16, lg: 8, xl: 8 };
const { Title } = Typography;
const approvalList = [
  {
    title: '数据脱敏审批',
    path: '/approval/mask-config',
    key: 'APPROVAL_MASK_CONFIG',
  },
];
const Dashboard: React.FC = () => {
  return (
    <PageContainer header={{ title: false }}>
      <div>
        <ProCard gutter={24} title="审批管理" wrap>
          {approvalList.map((item, index) => (
            <ProCard
              style={{ marginBottom: 24 }}
              colSpan={colSpan}
              bordered
              hoverable={true}
              key={index}
              onClick={() => {
                history.push(item.path);
              }}
            >
              <Row
                gutter={24}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Col lg={8} md={8} sm={8}>
                  <img src="/images/approval.png" width={48} style={{ float: 'inline-end' }} />
                </Col>
                <Col lg={16} md={16} sm={16}>
                  <Title level={5} style={{ marginTop: 10 }}>
                    {item.title}
                  </Title>
                </Col>
              </Row>
            </ProCard>
          ))}
        </ProCard>
      </div>
    </PageContainer>
  );
};

export default Dashboard;
