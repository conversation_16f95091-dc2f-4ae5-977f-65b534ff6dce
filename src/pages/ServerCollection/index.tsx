import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { batchDelUsingDELETE, getAllUsingGET1 } from '@/services/dsp/dataCollectionController';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import ServerCollectionDrawerForm from './components/ServerCollectionDrawerForm';

const ServerCollection: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.DataCollection[]>([]);
  const [initialValues, setInitialValues] = useState<API.DataCollection | undefined>();
  const [drawerVisit, setDrawerVisit] = useState(false);

  const onEdit = (record: API.DataCollection) => {
    setInitialValues(record);
    setDrawerVisit(true);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDelUsingDELETE(ids), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 100) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.DataCollection[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除采集“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.DataCollection>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 150,
      ellipsis: true,
    },
    {
      title: 'Bootstrap服务器名称',
      dataIndex: 'bootstrapServersConfig',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              key="edit"
              type="link"
              className="ant-btn-link-table"
              onClick={() => onEdit(record)}
            >
              编辑
            </Button>
            <Button
              type="link"
              className="ant-btn-link-table"
              key="del"
              onClick={() => handleDelete([record])}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.DataCollection>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columns={columns}
        headerTitle="服务器采集列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setDrawerVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建采集
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUsingGET1Params>({ ...params }, getAllUsingGET1)
        }
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      {/* 新建/编辑模版 */}
      <ServerCollectionDrawerForm
        initialValues={initialValues}
        open={drawerVisit}
        onOpenChange={(visible) => {
          setDrawerVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default ServerCollection;
