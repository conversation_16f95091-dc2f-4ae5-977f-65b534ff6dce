import {
  addUsingPOST,
  editUsingPUT,
  testLinkUsingPOST,
} from '@/services/dsp/dataCollectionController';
import { requiredRule } from '@/utils/setting';
import { BootstrapServersReg } from '@/utils/validator';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Space } from 'antd';
import React, { useRef } from 'react';

const ServerCollectionDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.id;
  const formRef = useRef<ProFormInstance>();
  const disabledRef = useRef(true);

  const { run: linkTest, loading: linkLoading } = useRequest((value) => testLinkUsingPOST(value), {
    manual: true,
    onSuccess: (res) => {
      const { code } = res;
      if (code !== 100) return;
      message.success('连接成功');
      disabledRef.current = false;
    },
    formatResult: (res) => res,
  });

  return (
    <DrawerForm<API.DataCollection>
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      submitter={{
        submitButtonProps: { disabled: disabledRef.current },
        //自定义整个区域
        render: (props, doms) => {
          return (
            <Space>
              {doms?.[0]}
              <Button
                type="primary"
                key="linkTest"
                onClick={async () => {
                  const formData =
                    (await formRef.current?.validateFieldsReturnFormatValue?.()) || {};
                  linkTest({ ...formData });
                }}
                loading={linkLoading}
              >
                测试连接
              </Button>
              {doms?.[1]}
            </Space>
          );
        },
      }}
      onFinish={async (value) => {
        const msg = isEdit ? await editUsingPUT(value) : await addUsingPOST(value);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        disabledRef.current = true;
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProFormText
        name="name"
        label="名称"
        placeholder="请输入"
        rules={[requiredRule]}
        disabled={isEdit}
      />
      <ProFormTextArea
        name="bootstrapServersConfig"
        label="Bootstrap服务器地址"
        placeholder="请输入"
        tooltip="请输入服务器地址，格式为IP+端口,多个地址请用,分割！"
        rules={[
          requiredRule,
          {
            pattern: BootstrapServersReg,
            message: '格式必须为IP+端口,多个地址请用,分割',
          },
        ]}
        fieldProps={{
          autoSize: {
            minRows: 1,
            maxRows: 3,
          },
        }}
      />
      <ProFormText name="username" label="用户名" placeholder="请输入" />
      <ProFormText.Password name="password" label="密码" placeholder="请输入" />
    </DrawerForm>
  );
};

export default ServerCollectionDrawerForm;
