import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import BaseListContext from '@/components/Context/BaseListContext';
import { useDBList } from '@/components/hooks/useDBList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { USER_STATUS } from '@/enums';
import {
  batchDelUsingPOST1,
  execSyncUsingPOST1,
  getAllUsingGET9,
} from '@/services/dsp/syncConfigController';
import { option2enum, queryPagingTable, tablesTransform } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import SyncConfigDrawerForm from './components/SyncConfigDrawerForm';

const DataSynchronization: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.SyncConfig[]>([]);
  const [initialValues, setInitialValues] = useState<API.SyncConfig | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [rowId, setRowId] = useState<number>();
  const { dbList, loading: dbLoading } = useDBList();

  const onEdit = (record: API.SyncConfig) => {
    const { tables } = record;
    const records = {
      ...record,
      initCheckedKeys: tablesTransform(tables),
    };
    setInitialValues(records);
    setModalVisit(true);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDelUsingPOST1(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = async (rows: API.SyncConfig[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    if (ids.length > 0 || names.length > 0) {
      Modal.confirm({
        title: '确认删除',
        content: `您确定要删除同步“${names.join('、')}”吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          deleteRecord(ids);
        },
      });
    }
  };
  //停止or启用
  const { run: disableRecord, loading } = useRequest((row) => execSyncUsingPOST1(row), {
    manual: true,
    onSuccess: (data, params) => {
      const text = params.at(0).status === 0 ? '启用' : '停止';
      message.success(`${text}成功`);
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDisabled = async (record: API.SyncConfig) => {
    const text = record.status === 0 ? '停止' : '启用';
    Modal.confirm({
      title: `确认“${text}”`,
      content: `您确定要${text}同步“${record.name}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        const status = record.status === 0 ? 1 : 0;
        const row = { ...record, status };
        disableRecord(row);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.SyncConfig>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 150,
      ellipsis: true,
    },
    {
      title: '源数据库',
      dataIndex: 'listenerId',
      valueType: 'select',
      fieldProps: {
        loading: dbLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'dbName',
        },
        options: dbList,
      },
      width: 150,
      ellipsis: true,
    },
    {
      title: '目标数据库',
      dataIndex: 'dbName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: option2enum(USER_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            {record.status !== 0 && (
              <a key="edit" onClick={() => onEdit(record)}>
                编辑
              </a>
            )}
            <Button
              type="link"
              className="ant-btn-link-table"
              key="disabled"
              onClick={() => handleDisabled(record)}
              loading={loading && rowId === record.id}
            >
              {record.status === 0 ? '停止' : '启用'}
            </Button>
            <a
              key="view"
              onClick={() => {
                history.push('/data-synchronization/sync-details', {
                  ...record,
                });
              }}
            >
              详情
            </a>
            {record.status !== 0 && (
              <a key="del" onClick={() => handleDelete([record])}>
                删除
              </a>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.SyncConfig>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: record?.status !== 1,
          }),
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="同步列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建同步
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUsingGET9Params>({ ...params }, getAllUsingGET9)
        }
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <BaseListContext.Provider value={{ dbList, dbLoading }}>
        <SyncConfigDrawerForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default DataSynchronization;
