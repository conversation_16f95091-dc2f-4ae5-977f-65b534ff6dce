import BaseListContext from '@/components/Context/BaseListContext';
import SchemaTable from '@/components/SchemaTableTree/index';
import { addUsingPOST3, modifyConfigUsingPUT1 } from '@/services/dsp/syncConfigController';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Divider, message, TreeProps } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useContext, useRef } from 'react';

const SyncConfigDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.id;
  const initialCheckedKeys = initialValues?.initCheckedKeys;
  const formRef = useRef<ProFormInstance>();
  const tableNamesRef = useRef<string[]>([]);
  const { dbList, dbLoading } = useContext(BaseListContext);

  return (
    <DrawerForm<API.DataListenerConfig>
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        let tables = '';
        if (tableNamesRef.current.length > 0) {
          tables = tableNamesRef.current.toString();
        } else {
          tables = initialValues?.tables;
        }
        const formData = {
          ...value,
          status: isEdit ? initialValues?.status : 1,
          tables: tables,
        };
        const msg = isEdit ? await modifyConfigUsingPUT1(formData) : await addUsingPOST3(formData);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
        <ProFormText name="dbId" label="dbId" />
      </div>
      <ProFormText
        name="name"
        label="名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
        disabled={isEdit}
      />
      <ProFormSelect
        name="listenerId"
        label="源数据库"
        rules={[requiredRule]}
        fieldProps={{
          allowClear: false,
          loading: dbLoading,
          showSearch: true,
          fieldNames: {
            value: 'id',
            label: 'dbName',
          },
        }}
        options={dbList as DefaultOptionType[]}
        disabled={isEdit}
      />
      <ProFormSelect
        name="targetDbId"
        label="目标数据库"
        rules={[requiredRule]}
        fieldProps={{
          allowClear: false,
          loading: dbLoading,
          showSearch: true,
          fieldNames: {
            value: 'id',
            label: 'dbName',
          },
        }}
        options={dbList as DefaultOptionType[]}
      />
      {/* 树型结构 */}
      <ProFormDependency key="listenerId" name={['listenerId']} ignoreFormListField>
        {({ listenerId }) => {
          if (listenerId) {
            return (
              <>
                <Divider type="horizontal"></Divider>
                <SchemaTable
                  dbId={listenerId}
                  initCheckedKeys={initialCheckedKeys}
                  onCheck={(values: TreeProps['checkedKeys']) => {
                    tableNamesRef.current = (
                      values as {
                        checked: string[];
                        halfChecked: string[];
                      }
                    ).checked?.filter((item) => (item as string).indexOf('.'));
                  }}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default SyncConfigDrawerForm;
