import { compareUsingPOST, syncInfoUsingPOST } from '@/services/dsp/syncConfigController';
import { editPageData } from '@/utils';
import { PageContainer, ProFormField } from '@ant-design/pro-components';
import { useLocation, useRequest } from '@umijs/max';
import { Badge, Button, Card, Descriptions, Divider, message, Modal, Spin } from 'antd';
import React from 'react';

const SyncDetails: React.FC = () => {
  const location = useLocation();
  const locationState = location.state as API.SyncConfig;
  const detailData = editPageData(locationState);
  const status = detailData?.status === 0;
  const { data = {}, loading } = useRequest(() => syncInfoUsingPOST(detailData), {
    manual: !detailData,
    pollingInterval: 5000,
  });

  // 修复
  const { run: compareRecord } = useRequest((detailData) => compareUsingPOST(detailData), {
    manual: true,
    onSuccess: (res) => {
      if (res.code === 100) {
        message.success('操作成功');
      } else {
        message.error(res.data);
      }
    },
    formatResult: (res) => res,
  });
  const handleCompare = async () => {
    Modal.confirm({
      title: '确认比较',
      content: `您确定要比较同步配置“${detailData.name}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        compareRecord({ ...detailData });
      },
    });
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <Card bordered={false}>
        <Spin spinning={loading}>
          <Descriptions
            title="同步详情"
            style={{ marginBottom: 24 }}
            extra={
              <Button type="primary" onClick={handleCompare}>
                比较
              </Button>
            }
            column={4}
          >
            <Descriptions.Item label="已同步数据量">{data?.syncedNum}</Descriptions.Item>
            <Descriptions.Item label="待同步数据量">{data?.noSyncNum}</Descriptions.Item>
            <Descriptions.Item label="总数据量">{data?.totalNum}</Descriptions.Item>
            <Descriptions.Item label="状态">
              <Badge status={status ? 'success' : 'error'} text={status ? '启用' : '停止'} />
            </Descriptions.Item>
          </Descriptions>
          <Divider style={{ marginBottom: 24 }} />
        </Spin>
        <Descriptions title="同步日志">
          <Descriptions.Item style={{ backgroundColor: 'rgb(246, 248, 250)' }}>
            <ProFormField valueType="jsonCode" text={data?.detailLogs} mode="read" />
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </PageContainer>
  );
};

export default SyncDetails;
