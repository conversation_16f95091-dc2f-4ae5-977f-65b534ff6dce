import BaseListContext from '@/components/Context/BaseListContext';
import { useDBList } from '@/components/hooks/useDBList';
import { useUserList } from '@/components/hooks/useUserList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { APPROVAL_STATUS, MASK_CONFIG_STATUS, MASK_RULE_TYPES } from '@/enums';
import { addApprovalListUsingPOST } from '@/services/dsp/approvalListController';
import {
  batchDelMaskConfigUsingDELETE,
  executeMaskConfigUsingPOST,
  getMaskConfigUsingGET,
  stopMaskByMaskConfigIdUsingPOST,
} from '@/services/dsp/maskConfigController';
import {
  getMaskRuleUsingGET,
  saveBatchMaskRulesUsingPOST,
} from '@/services/dsp/maskRuleController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  DrawerForm,
  PageContainer,
  ProColumns,
  ProFormDependency,
  ProFormDigit,
  ProFormFieldSet,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { history, useLocation, useModel, useRequest } from '@umijs/max';
import { useReactive } from 'ahooks';
import { Button, Form, message, Modal, Space, Table } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React, { useEffect, useRef, useState } from 'react';
import MaskConfigModalForm from './components/MaskConfigModalForm';

interface MaskConfigProps {
  [key: string]: any;
  currentRecord: API.MaskRuleVo;
  tableData: API.MaskRuleVo[];
}
const tabKeys = [
  {
    label: '与我相关',
    key: '0',
  },
  {
    label: '我发起',
    key: '1',
  },
  {
    label: '我处理',
    key: '2',
  },
];

const MaskConfig: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.MaskConfig[]>([]);
  const [initialValues, setInitialValues] = useState<API.MaskConfig | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const { dbList, loading: dbLoading } = useDBList();
  const [rowId, setRowId] = useState<number>();
  const formRef = useRef<ProFormInstance>();
  const [discontinueId, setDiscontinueId] = useState<number>();
  const [disabled, setDisabled] = useState<boolean>(false);
  const { search, pathname } = useLocation();
  // 解析URL查询参数
  const queryParams = new URLSearchParams(search);
  const currentKey = queryParams.get('approvalListStatus') || '0';
  const [activeKey, setActiveKey] = useState<string>(currentKey);
  //获取当前用户
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  useEffect(() => {
    history.push({ pathname, search: `approvalListStatus=${activeKey}` });
  }, [activeKey]);

  //策略配置相关
  const maskRule = useReactive<MaskConfigProps>({
    visible: false,
    formVisible: false,
    currentRecord: {},
    tableData: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    maskConfigId: 0,
    approvalStatus: 0,
  });

  const handleRules = (record: API.MaskRuleVo) => {
    maskRule.currentRecord = record;
    maskRule.formVisible = true;
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDelMaskConfigUsingDELETE(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });

  // 查询脱敏策略配置
  const { run: getMaskRule } = useRequest(
    () =>
      getMaskRuleUsingGET({
        pageNum: maskRule.pageNum,
        pageSize: maskRule.pageSize,
        maskConfigId: maskRule.maskConfigId,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        maskRule.total = res?.total;
        maskRule.tableData = res?.records;
        maskRule.visible = true;
      },
    },
  );

  const handleDelete = async (rows: API.MaskConfig[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.maskName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  //执行
  const { run: runRecord } = useRequest(
    (maskConfigId) => executeMaskConfigUsingPOST({ maskConfigId }),
    {
      manual: true,
      onSuccess: () => {
        message.success('操作成功');
        setRowId(undefined);
        tableRef.current?.reloadAndRest?.();
      },
    },
  );
  const handleRun = async (record: API.MaskConfig) => {
    Modal.confirm({
      title: '确认执行',
      content: `您确定要执行“${record.maskName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        runRecord(record.id);
      },
    });
  };

  const { run: discontinueRecord } = useRequest(
    (maskConfig) => stopMaskByMaskConfigIdUsingPOST(maskConfig, { skipErrorHandler: true }),
    {
      manual: true,
      onSuccess: () => {
        message.success('操作成功');
      },
    },
  );
  const handleDiscontinue = async (record: API.MaskConfig) => {
    Modal.confirm({
      title: '确认中止',
      content: `您确定要中止“${record.maskName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setDiscontinueId(record.id);
        discontinueRecord(record);
        setDisabled(true);
      },
    });
  };

  //审批相关
  const [form] = useForm();
  const { userList, loading } = useUserList();

  //提交审核
  const { run: submitApproval } = useRequest((value) => addApprovalListUsingPOST(value), {
    manual: true,
    onSuccess: (res) => {
      form.resetFields();
      if (res.code !== 100) return;
      message.success('提交成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
    onError: (err) => {
      form.resetFields();
      console.log('🚀 ~ const{run:submitApproval}=useRequest ~ err:', err);
    },
  });
  const handleSubmit = async (record: Record<string, any>) => {
    Modal.confirm({
      title: `确认提交审批`,
      content: (
        <Form form={form}>
          <ProFormSelect
            name="auditorId"
            label="审批人"
            width="md"
            placeholder="请选择"
            options={userList.filter((item: API.User) => item.id !== currentUser?.id)}
            fieldProps={{
              showSearch: true,
              loading,
              fieldNames: {
                label: 'userName',
                value: 'id',
              },
            }}
            rules={[requiredRule]}
          />
          <ProFormTextArea
            width="md"
            name="description"
            label="描述"
            placeholder="请输入"
            fieldProps={{ autoSize: { minRows: 1, maxRows: 2 } }}
          />
        </Form>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const isRequired = await form.validateFields(['auditorId']);
        const description = form.getFieldValue('description');
        const auditorId = isRequired?.auditorId;
        const approvalData = {
          approvalStatus: record?.approvalListStatus || 0,
          auditorId,
          description,
          listerId: currentUser?.id,
          processingId: record.id,
          funcListId: 0,
        };
        submitApproval(approvalData);
      },
      onCancel: () => {
        form.resetFields();
      },
    });
  };

  // 表格
  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '名称',
      dataIndex: 'maskName',
      width: 150,
      fixed: 'left',
      ellipsis: true,
      search: false,
    },
    {
      title: '数据扫描',
      dataIndex: 'scanSensitiveName',
      width: 140,
      ellipsis: true,
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      ellipsis: true,
      search: false,
    },
    {
      title: '最后执行时间',
      dataIndex: 'lastTime',
      width: 150,
      ellipsis: true,
      search: false,
    },
    {
      title: '最后状态',
      dataIndex: 'lastStatus',
      valueEnum: option2enum(MASK_CONFIG_STATUS),
      width: 90,
      ellipsis: true,
      search: false,
    },
    {
      title: '当前状态',
      dataIndex: 'status',
      valueEnum: option2enum(MASK_CONFIG_STATUS),
      width: 90,
      search: false,
      ellipsis: true,
    },
    {
      title: '审批状态',
      dataIndex: 'approvalListStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      width: 90,
      ellipsis: true,
      search: false,
    },
    {
      title: '审批状态',
      dataIndex: 'approvalListStatus',
      valueEnum: option2enum(APPROVAL_STATUS),
      hideInTable: true,
    },
    {
      title: '审批人',
      dataIndex: 'auditorName',
      width: 100,
      ellipsis: true,
      search: false,
      hideInTable: activeKey === '2',
    },
    {
      title: '提交人',
      dataIndex: 'listerName',
      width: 100,
      ellipsis: true,
      search: false,
      hideInTable: activeKey === '1',
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { approvalListStatus } = record;
        const isRunLoading = rowId === record.id || record.status === '0';
        const isdisLoading = discontinueId === record.id;
        if (isdisLoading && record.status === '3') {
          setDiscontinueId(undefined);
          setDisabled(false);
        }
        /* 显示提交按钮 与我相关、 我发起中 审核状态为null */
        const isSubmit =
          ['0', '1'].includes(activeKey) &&
          (approvalListStatus === null || approvalListStatus === undefined);

        //显示执行或中止按钮  我发起、与我相关中 审批状态为审核通过
        const isRunOrStop = ['0', '1'].includes(activeKey) && approvalListStatus === 1;
        return (
          <Space>
            {isSubmit && (
              <Button
                key="submit"
                type="link"
                className="ant-btn-link-table"
                onClick={() => handleSubmit(record)}
              >
                提交审批
              </Button>
            )}
            <Button
              key="rule"
              type="link"
              className="ant-btn-link-table"
              onClick={() => {
                maskRule.total = 0;
                maskRule.pageNum = 1;
                maskRule.maskConfigId = record.id as number;
                maskRule.approvalStatus = record.approvalListStatus as number;

                getMaskRule();
              }}
              disabled={record.status === '0'}
            >
              脱敏策略
            </Button>
            {isRunOrStop && (
              <Button
                type="link"
                className="ant-btn-link-table"
                key="run"
                onClick={() => handleRun(record)}
                loading={isRunLoading}
                disabled={record.status === '0'}
              >
                执行
              </Button>
            )}
            <Button
              key="view"
              type="link"
              className="ant-btn-link-table"
              onClick={() => {
                history.push(`/data-desensitization/mask-config/mask-config-details/${record.id}`);
              }}
            >
              详情
            </Button>
            {isRunOrStop && record.status === '0' && (
              <Button
                type="link"
                className="ant-btn-link-table"
                key="discontinue"
                loading={isdisLoading}
                onClick={() => handleDiscontinue(record)}
                disabled={disabled || record.status !== '0'}
              >
                中止
              </Button>
            )}
            {isSubmit && (
              <Button
                key="del"
                type="link"
                className="ant-btn-link-table"
                onClick={() => handleDelete([record])}
                disabled={record.status === '0'}
              >
                删除
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  const columns2: any = [
    {
      title: '项目空间',
      dataIndex: 'schemaName',
    },
    {
      title: '脱敏方式',
      dataIndex: 'maskType',
      render: (_: any, record: API.MaskRuleVo) => {
        return <span>{MASK_RULE_TYPES.find((i) => i.value === record.maskType)?.label}</span>;
      },
    },
    {
      title: '表名',
      dataIndex: 'tableName',
    },
    {
      title: '列名',
      dataIndex: 'fieldName',
    },
    {
      title: '敏感类型',
      dataIndex: 'ruleName',
    },
    {
      title: '操作',
      width: 90,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (_: any, record: API.MaskRuleVo) => {
        return (
          <Space>
            {record.ruleName !== '非敏感字段' && (
              <Button
                type="link"
                className="ant-btn-link-table"
                key="edit"
                onClick={() => handleRules(record)}
              >
                规则配置
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.MaskConfig>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="数据脱敏列表"
        params={{
          approvalListStatus: activeKey,
        }}
        form={{
          syncToUrl: true,
          syncToInitialValues: false,
          extraUrlParams: {
            approvalListStatus: activeKey,
          },
        }}
        toolbar={{
          multipleLine: true,
          tabs: {
            activeKey,
            items: tabKeys,
            onChange: (key) => setActiveKey(key as string),
          },
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建脱敏
            </Button>,
          ],
        }}
        /** 表格请求参数中approvalListStatus表示与我相关、我发起、我处理的状态
         *而请求返回的数据中approvalListStatus表示当前的审批状态，待审批、已通过、已拒绝
         */
        request={async (params) => {
          const { approvalListStatus, current, pageSize } = params;
          return queryPagingTable(
            {
              pageNum: current,
              pageSize,
              userId: currentUser?.id,
              approvalListStatus: parseInt(approvalListStatus),
            },
            getMaskConfigUsingGET,
          );
        }}
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <BaseListContext.Provider value={{ dbList, dbLoading }}>
        <MaskConfigModalForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
      <Modal
        footer={null}
        onCancel={() => (maskRule.visible = false)}
        title="查看详情"
        open={maskRule.visible}
        width={'60%'}
      >
        <Table
          dataSource={maskRule.tableData}
          rowKey="id"
          columns={columns2}
          size={'small'}
          pagination={{
            total: maskRule.total,
            pageSize: maskRule.pageSize,
            onChange: (page) => {
              maskRule.pageNum = page;
              getMaskRule();
            },
          }}
        />
      </Modal>
      <DrawerForm<API.MaskRuleVo>
        width={460}
        title={'规则配置'}
        formRef={formRef}
        open={maskRule.formVisible}
        onOpenChange={(visible) => {
          maskRule.formVisible = visible;
        }}
        disabled={[0, 1, 2].includes(maskRule.approvalStatus)}
        submitter={{
          render: (props, dom) => {
            return ![0, 1, 2].includes(maskRule.approvalStatus) && [...dom];
          },
        }}
        initialValues={{
          ...maskRule.currentRecord,
          effectRange: maskRule.currentRecord?.effectRange?.split(',') ?? [],
        }}
        onFinish={async (value) => {
          const formData = [
            {
              ...maskRule.currentRecord,
              ...value,
              effectRange: value.effectRange?.endsWith(',')
                ? value.effectRange.replace(',', '')
                : value.effectRange,
              maskConfigId: maskRule.maskConfigId,
            },
          ];
          const msg = await saveBatchMaskRulesUsingPOST(formData);
          const success = msg.code === 100;
          if (success) {
            message.success('操作成功!');
            getMaskRule();
          }
          return success;
        }}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
          maskClosable: false,
        }}
      >
        <ProFormRadio.Group
          name="maskType"
          label="脱敏方式"
          options={
            maskRule.currentRecord.isNumber === 1
              ? MASK_RULE_TYPES
              : MASK_RULE_TYPES.filter((i) => i.value !== 'ROUND')
          }
        />
        <ProFormDependency key="maskType" name={['maskType']} ignoreFormListField>
          {({ maskType }) => {
            if (maskType === 'COVER') {
              return (
                <>
                  <ProFormText
                    name="coverChar"
                    label="覆盖字符"
                    placeholder="请输入"
                    fieldProps={{
                      autoComplete: 'none',
                      maxLength: 1,
                    }}
                    rules={[requiredRule]}
                  />
                  <ProFormFieldSet
                    name="effectRange"
                    label="影响区间"
                    type="group"
                    required
                    rules={[
                      {
                        validator: (_, value, callback) => {
                          console.warn('🚀 ~ value:', value);
                          const [name, name1] = value || [];
                          if (!name) {
                            return callback('第一个值不能为空');
                          }
                          if (name && name1 && name1 < name) {
                            return callback('第二个值必须大于等于第一个值');
                          }
                          return callback();
                        },
                      },
                    ]}
                    transform={(value: number[]) => {
                      return {
                        effectRange: value?.length === 1 ? String(value[0]) : value?.join(','),
                      };
                    }}
                  >
                    <ProFormDigit
                      placeholder={'开始位置'}
                      min={1}
                      max={1000000}
                      fieldProps={{ precision: 0 }}
                    />
                    &nbsp;&nbsp;&nbsp;
                    <ProFormDigit
                      placeholder={'结束位置'}
                      min={1}
                      max={1000000}
                      fieldProps={{ precision: 0 }}
                    />
                  </ProFormFieldSet>
                </>
              );
            }
            if (maskType === 'ROUND') {
              return (
                <ProFormDigit
                  name="keepOffset"
                  label="保留位数"
                  placeholder={'请输入'}
                  min={1}
                  max={1000000}
                  fieldProps={{ precision: 0 }}
                  rules={[requiredRule]}
                />
              );
            }
            if (maskType === 'HASH') {
              return (
                <ProFormDigit
                  name="salt"
                  label="加盐值"
                  placeholder={'请输入'}
                  min={0}
                  max={9}
                  fieldProps={{ precision: 0 }}
                  rules={[requiredRule]}
                />
              );
            }
          }}
        </ProFormDependency>
      </DrawerForm>
    </PageContainer>
  );
};

export default MaskConfig;
