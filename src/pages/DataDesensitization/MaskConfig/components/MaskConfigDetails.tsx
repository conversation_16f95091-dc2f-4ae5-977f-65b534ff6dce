import { MASK_CONFIG_STATUS } from '@/enums';
import { getAllMaskConfigInfoUsingGET } from '@/services/dsp/maskConfigInfoController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { request, useLocation } from '@umijs/max';
import { Button, Drawer, message } from 'antd';
import React, { useRef, useState } from 'react';

const MaskConfigDetails: React.FC = () => {
  //详情页面
  const location = useLocation();
  const url = location.pathname;
  const maskConfigId = url.substring(url.lastIndexOf('/') + 1, url.length);
  const tableRef = useRef<ActionType | undefined>();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [errorText, setErrorText] = useState('');

  const handleDownloadExcel = (record: DSP_API.ManualDetailsData) => {
    const { filePath } = record;
    const fileName = filePath.split('\\').pop();

    request('/api/maskConfigInfo/fileDownload', {
      method: 'POST',
      data: record,
      responseType: 'blob',
      getResponse: true,
      skipErrorHandler: true,
    })
      .then((response) => {
        const url = URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        link.setAttribute('download', fileName!); // 指定下载的文件名和类型
        document.body.appendChild(link);
        link.click();
      })
      .catch(() => {
        message.error('下载失败');
      });
  };

  const columns: ProColumns<DSP_API.ManualDetailsData>[] = [
    {
      title: '开始时间',
      width: 180,
      dataIndex: 'createTime',
    },
    {
      title: '完成时间',
      width: 180,
      dataIndex: 'endTime',
    },
    {
      title: '总条数',
      width: 100,
      dataIndex: 'total',
    },
    {
      title: '已处理',
      width: 100,
      dataIndex: 'masked',
    },
    {
      title: '执行状态',
      width: 80,
      dataIndex: 'status',
      valueEnum: option2enum(MASK_CONFIG_STATUS),
    },
    {
      title: '错误信息',
      dataIndex: 'exception',
      render: (text, record) => {
        return (
          <span
            title={'点击查看详情'}
            style={{
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: 2,
              overflow: 'hidden',
              color: 'var(--primary-color)',
              cursor: 'pointer',
            }}
            onClick={() => {
              setErrorText(record.exception);
              setDrawerVisible(true);
            }}
          >
            {record.exception}
          </span>
        );
      },
    },
    {
      title: '操作',
      width: 90,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return record?.filePath ? (
          <Button
            type="link"
            className="ant-btn-link-table"
            key="downloadExcel"
            onClick={() => handleDownloadExcel(record)}
          >
            导出
          </Button>
        ) : (
          <></>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<any>
        {...defaultTableConfig}
        actionRef={tableRef}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="详情列表"
        request={async (params) =>
          queryPagingTable<API.getAllMaskConfigInfoUsingGETParams>(
            { ...params, maskConfigId },
            getAllMaskConfigInfoUsingGET,
          )
        }
        polling={5000}
      />
      <Drawer
        destroyOnClose
        width={'40%'}
        title="错误信息详情"
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
      >
        <p
          style={{
            padding: '15px',
            lineHeight: '1.45',
            whiteSpace: 'pre',
          }}
        >
          {errorText}
        </p>
      </Drawer>
    </PageContainer>
  );
};

export default MaskConfigDetails;
