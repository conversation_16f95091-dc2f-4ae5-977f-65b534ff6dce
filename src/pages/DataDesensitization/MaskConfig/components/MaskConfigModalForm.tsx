import BaseListContext from '@/components/Context/BaseListContext';
import SchemaTableColumnTree from '@/pages/DataDesensitization/MaskConfig/components/SchemaTableColumnTree';
import {
  addMaskConfigUsingPOST,
  modifyMaskConfigUsingPOST,
} from '@/services/dsp/maskConfigController';
import { getAllScanSensitiveUsingGET } from '@/services/dsp/scanSensitiveController';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  ModalForm,
  ModalFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { useReactive } from 'ahooks';
import { Button, Divider, message, Table } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { UploadFile } from 'antd/es/upload/interface';
import React, { useContext, useEffect, useRef, useState } from 'react';

type dbList = {
  sourceTable: string;
  targetTable: string;
  dbId: string | number;
};

interface DbStateProps {
  dbList: dbList[] | [];
  visible: boolean;
  currentSourceTable: string;
}

export const getStrategyList = (strategyObj: { [key: string]: string }) => {
  return Object.entries(strategyObj).map(([key, value]) => {
    return {
      columnsName: key,
      codeName: value,
    };
  });
};
const MaskConfigModalForm: React.FC<ModalFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  // 判断是否是新增
  const isEdit = initialValues?.id;
  const formRef = useRef<ProFormInstance>();
  const targetRef = useRef<ProFormInstance>();
  const strategyRef = useRef<{ [key: string]: string }>({});
  const [, setUpLoadFile] = useState<UploadFile>();
  const filePathRef = useRef<string>('');
  //确定按钮
  const [, setFileDisabled] = useState<boolean>(true);
  //dbType为MONGODB时
  const tableNamesRef = useRef<string[]>([]);
  const { dbList, dbLoading } = useContext(BaseListContext);

  const dbState = useReactive<DbStateProps>({
    dbList: [],
    visible: false,
    currentSourceTable: '',
  });

  const clearData = () => {
    strategyRef.current = {};
    setUpLoadFile(undefined);
    filePathRef.current = '';
    setFileDisabled(true);
    tableNamesRef.current = [];
  };

  useEffect(() => {
    dbState.dbList = [];
  }, [open]);

  const { data: scanSensitiveData, loading: scanSensitiveLoading } = useRequest(
    () => getAllScanSensitiveUsingGET({ pageNum: 1, pageSize: 10000 }),
    {
      onSuccess: () => {},
      formatResult: (res) => {
        return res?.data?.records;
      },
    },
  );

  const handleTarget = (record: dbList) => {
    dbState.currentSourceTable = record?.sourceTable;
    dbState.visible = true;
  };

  const columns: any = [
    {
      title: '源表',
      dataIndex: 'sourceTable',
    },
    {
      title: '目标表',
      dataIndex: 'targetTable',
    },
    {
      title: '操作',
      width: 90,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (_: any, record: dbList) => {
        return (
          <Button
            type="link"
            className="ant-btn-link-table"
            key="edit"
            onClick={() => handleTarget(record)}
          >
            {record?.targetTable ? '重新配置' : '配置目标表'}
          </Button>
        );
      },
    },
  ];

  return (
    <ModalForm
      width={660}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={(val) => {
        clearData();
        onOpenChange?.(val);
      }}
      onFinish={async (value) => {
        if (
          formRef.current?.getFieldValue('targetType') === 'db' &&
          dbState.dbList.length > 0 &&
          dbState.dbList.some((i) => !i.targetTable || !i.dbId)
        ) {
          message.error('请完善目标表信息');
          return;
        }
        let formData = {};
        if (formRef.current?.getFieldValue('targetType') === 'db' && dbState.dbList.length > 0) {
          formData = {
            ...value,
            tableNames: dbState.dbList.map((i) => `${i.sourceTable}>${i.targetTable}`).join(','),
            dbId: dbState.dbList.map((i) => String(i.dbId)).join(','), //数据扫描配置目标数据库时为一对一或一对多
          };
        } else {
          formData = {
            ...value,
            waterMark: value.waterMark === true ? 1 : 0,
          };
        }
        const msg = isEdit
          ? await modifyMaskConfigUsingPOST(formData)
          : await addMaskConfigUsingPOST(formData);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
          clearData();
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      modalProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
        <ProFormText name="initKeys" label="initKeys" placeholder="请输入" />
      </div>
      <ProFormText
        name="maskName"
        label="名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormSelect
        name="scanSensitiveId"
        label="数据扫描"
        rules={[requiredRule]}
        options={scanSensitiveData as DefaultOptionType[]}
        fieldProps={{
          allowClear: false,
          loading: scanSensitiveLoading,
          fieldNames: {
            value: 'id',
            label: 'scanName',
          },
          onChange: (val) => {
            dbState.dbList = [];

            const selectedSensitive = scanSensitiveData.find(
              (i: API.ScanSensitive) => i.id === val,
            );
            if (selectedSensitive?.tables) {
              const arr: dbList[] = [];
              selectedSensitive.tables?.split(',')?.forEach((i: string) => {
                arr.push({
                  sourceTable: i,
                  targetTable: '',
                  dbId: '',
                });
              });
              dbState.dbList = arr;
            }

            formRef.current?.setFieldValue('targetDbId', undefined);
          },
        }}
      />
      <ProFormSelect
        name="targetType"
        label="类型"
        valueEnum={{
          file: '文件',
          db: '数据库',
        }}
        rules={[requiredRule]}
        disabled={isEdit}
        fieldProps={{
          allowClear: false,
          onChange: (val) => {
            dbState.dbList = [];

            const scanSensitiveId = formRef.current?.getFieldValue('scanSensitiveId');
            const selectedSensitive = scanSensitiveData.find(
              (i: API.ScanSensitive) => i.id === scanSensitiveId,
            );
            if (val === 'db' && selectedSensitive?.tables) {
              const arr: dbList[] = [];
              selectedSensitive.tables?.split(',')?.forEach((i: string) => {
                arr.push({
                  sourceTable: i,
                  targetTable: '',
                  dbId: '',
                });
              });
              dbState.dbList = arr;
            }
            clearData();
            formRef.current?.setFieldValue('targetDbId', undefined);
          },
        }}
      />

      <ProFormDependency key="targetTypeAndScanSensitive" name={['targetType']} ignoreFormListField>
        {({ targetType }) => {
          if (targetType === 'db' && dbState.dbList?.length && !isEdit) {
            return (
              <>
                <ProFormSelect
                  name="targetDbId"
                  label="目标数据库"
                  fieldProps={{
                    loading: dbLoading,
                    showSearch: true,
                    fieldNames: {
                      value: 'id',
                      label: 'dbName',
                    },
                    onChange: (value) => {
                      dbState.dbList = value
                        ? dbState.dbList.map((i) => ({
                            ...i,
                            dbId: value,
                            targetTable: i.sourceTable,
                          }))
                        : dbState.dbList.map((i) => ({
                            ...i,
                            dbId: '',
                            targetTable: '',
                          }));
                    },
                  }}
                  options={dbList as DefaultOptionType[]}
                />
                <Table
                  rowKey={'sourceTable'}
                  dataSource={dbState.dbList}
                  columns={columns}
                  size={'small'}
                  pagination={false}
                  scroll={{ x: '100%' }}
                />
              </>
            );
          }
          if (targetType === 'file') {
            return (
              <>
                <ProFormSwitch name="waterMark" label="数据水印" />
              </>
            );
          }
        }}
      </ProFormDependency>
      <DrawerForm
        width={460}
        title={'配置目标表'}
        formRef={targetRef}
        open={dbState.visible}
        onOpenChange={(visible) => {
          dbState.visible = visible;
        }}
        onFinish={async (value) => {
          let arr = [...dbState.dbList];
          arr = arr.filter((i) => i.sourceTable !== dbState.currentSourceTable);
          arr.push({
            ...value,
            sourceTable: dbState.currentSourceTable,
          });
          dbState.dbList = arr;
          return true;
        }}
        autoFocusFirstInput
        drawerProps={{
          destroyOnClose: true,
          maskClosable: false,
        }}
      >
        <div style={{ display: 'none' }}>
          <ProFormText name="targetTable" label="targetTable" placeholder="请输入" />
        </div>
        <ProFormSelect
          name="dbId"
          label="数据库"
          rules={[requiredRule]}
          options={dbList?.map((item) => ({
            label: item.dbName,
            value: item.id,
          }))}
          fieldProps={{
            allowClear: false,
            loading: dbLoading,
            showSearch: true,
          }}
        />
        <ProFormDependency key="dbId" name={['dbId']} ignoreFormListField>
          {({ dbId }) => {
            if (dbId || dbId === 0) {
              return (
                <>
                  <Divider type="horizontal"></Divider>
                  <SchemaTableColumnTree
                    dbId={dbId}
                    initCheckedKeys={initialValues?.initCheckedKeys}
                    onCheckChange={(checkedKeys) => {
                      targetRef.current?.setFieldValue('targetTable', checkedKeys);
                    }}
                  />
                </>
              );
            }
          }}
        </ProFormDependency>
      </DrawerForm>
    </ModalForm>
  );
};

export default MaskConfigModalForm;
