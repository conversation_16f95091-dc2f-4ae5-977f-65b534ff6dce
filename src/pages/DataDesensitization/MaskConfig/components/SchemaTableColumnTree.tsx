import { fetchSchemasUsingGET, fetchTablesUsingGET } from '@/services/dsp/syncConfigController';
import { RowEditableConfig } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Spin, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

type SchemaTableColumnProps = {
  dbId?: number;
  initCheckedKeys?: any;
  originCheckedKeys?: string[]; //采集配置中已选择的数组
  onCheckChange?: (checkedKeys?: any) => void;
};
type Strategy = { columnsName: string; codeName?: string; schema_tableName?: string };

const SchemaTableColumnTree: React.FC<RowEditableConfig<Strategy> & SchemaTableColumnProps> = ({
  dbId,
  originCheckedKeys,
  onCheckChange,
}) => {
  const treeRef = useRef<
    {
      key: string;
      title: string;
      children?: {
        key: string;
        title: string;
        strategyList: { columnsName: string; codeName?: string }[];
      }[];
    }[]
  >([]);

  const [checkedTable, setCheckedTable] = useState<string>('');

  //获取schema
  const { run: querySchemas, loading: schemaLoading } = useRequest(
    (dbId) => fetchSchemasUsingGET({ dbId }, { timeout: 5 * 60 * 1000 }),
    {
      manual: true,
      onSuccess: (data) => {
        const schemas = data?.map((item: { schema: string }) => ({
          key: item.schema,
          title: item.schema,
          children: [],
        }));
        treeRef.current = schemas;
        //筛选
        if (originCheckedKeys) {
          treeRef.current = schemas.filter((item: { key: string }) =>
            originCheckedKeys.includes(item.key),
          );
        }
      },
    },
  );
  useEffect(() => {
    if (dbId) {
      querySchemas(dbId);
    }
  }, [dbId]);
  //获取tableS
  const { run: queryTables, loading: tableLoading } = useRequest(
    ({ dbId, schemaName }) => fetchTablesUsingGET({ dbId, schemaName }, { timeout: 5 * 60 * 1000 }),
    {
      manual: true,
      onSuccess: (data, params) => {
        const schemaName = params.at(0).schemaName;
        const tables = data?.map((item: { table_name: string }) => ({
          key: schemaName + '.' + item.table_name,
          title: item.table_name,
        }));
        treeRef.current = treeRef.current.map((item) => {
          if (item.key === schemaName) {
            //存在则筛选
            if (originCheckedKeys) {
              return {
                ...item,
                children: tables.filter((item: { key: string }) =>
                  originCheckedKeys.includes(item.key),
                ),
              };
            } else {
              return { ...item, children: tables };
            }
          }
          return item;
        });
      },
    },
  );

  return (
    <Spin spinning={schemaLoading || tableLoading}>
      <Table
        scroll={{ y: 340 }}
        pagination={false}
        columns={[
          {
            title: '数据库',
            dataIndex: 'key',
            key: 'title',
          },
        ]}
        expandable={{
          onExpand: (record, event) => {
            if (record && event?.key) {
              //如果是展开
              queryTables({ dbId: dbId, schemaName: event.key });
            }
          },
          indentSize: 20,
        }}
        rowSelection={{
          type: 'radio',
          selectedRowKeys: [checkedTable],
          onSelect: (record: any, checked) => {
            if (checked) {
              setCheckedTable(record?.key || '');
              onCheckChange?.(record?.key || '');
            } else {
              setCheckedTable('');
            }
          },
          getCheckboxProps: (record) => ({
            disabled: record.hasOwnProperty('children'),
          }),
        }}
        dataSource={treeRef.current}
      />
    </Spin>
  );
};

export default SchemaTableColumnTree;
