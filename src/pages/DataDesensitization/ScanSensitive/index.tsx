import BaseListContext from '@/components/Context/BaseListContext';
import { useDBList } from '@/components/hooks/useDBList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { MANUAL_LAST_STATUS, SCAN_TYPE } from '@/enums';
import {
  batchDelScanSensitiveUsingDELETE,
  getAllScanSensitiveUsingGET,
  scanSensitiveUsingPOST,
} from '@/services/dsp/scanSensitiveController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import ScanSensitiveModalForm from './components/ScanSensitiveModalForm';

const ScanSensitive: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.ScanSensitive[]>([]);
  const [initialValues, setInitialValues] = useState<API.ManualMaskConfig | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const { dbList, loading: dbLoading } = useDBList();
  const [rowId, setRowId] = useState<number>();

  const onEdit = (record: API.ScanSensitive) => {
    const recordData = {
      ...record,
      recognizeRulesIds: record?.recognizeRulesIds?.split(',')?.map((item) => Number(item)),
      dbId: Number(record?.dbId),
      initCheckedKeys: record.tables,
    };
    setModalVisit(true);
    setInitialValues(recordData);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDelScanSensitiveUsingDELETE(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = async (rows: API.ScanSensitive[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.scanName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  //执行
  const { run: runRecord } = useRequest(
    (scanSensitiveId) => scanSensitiveUsingPOST({ scanSensitiveId }),
    {
      manual: true,
      onSuccess: () => {
        message.success('操作成功');
        setRowId(undefined);
        tableRef.current?.reloadAndRest?.();
      },
    },
  );
  const handleRun = async (record: API.ScanSensitive) => {
    Modal.confirm({
      title: '确认执行',
      content: `您确定要进行扫描“${record.scanName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        runRecord(record.id);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.ScanSensitive>[] = [
    {
      title: '名称',
      dataIndex: 'scanName',
      width: 150,
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '扫描类型',
      dataIndex: 'dataSourceType',
      valueEnum: option2enum(SCAN_TYPE),
      width: 100,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 120,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: option2enum(MANUAL_LAST_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '错误信息',
      dataIndex: 'exception',
      width: 200,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              key="edit"
              type="link"
              className="ant-btn-link-table"
              disabled={record.status === 0}
              onClick={() => onEdit(record)}
            >
              编辑
            </Button>
            <Button
              type="link"
              className="ant-btn-link-table"
              key="run"
              onClick={() => handleRun(record)}
              loading={rowId === record.id || record.status === 0}
              disabled={record.status === 0}
            >
              执行
            </Button>
            <Button
              key="view"
              type="link"
              className="ant-btn-link-table"
              disabled={record.status === 0}
              onClick={() => {
                history.push(
                  `/data-desensitization/scan-sensitive/scan-sensitive-analysis/${record.id}`,
                );
              }}
            >
              详情
            </Button>
            <Button
              type="link"
              className="ant-btn-link-table"
              disabled={record.status === 0}
              key="del"
              onClick={() => handleDelete([record])}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.ScanSensitive>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="数据扫描列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建脱敏
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllManualMaskUsingGETParams>(
            { ...params },
            getAllScanSensitiveUsingGET,
          )
        }
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <BaseListContext.Provider value={{ dbList, dbLoading }}>
        <ScanSensitiveModalForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default ScanSensitive;
