import { viewGeneralUsingPOST } from '@/services/dsp/scanSensitiveController';
import { useLocation } from '@@/exports';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Col, Row, Space, Typography } from 'antd';
import React from 'react';

const colSpan = { xs: 12, sm: 12, md: 8, lg: 8, xl: 8 };
const { Text, Title } = Typography;
const ScanSensitiveAnalysis: React.FC = () => {
  const location = useLocation();
  const url = location.pathname;
  const scanSensitiveId = url.substring(url.lastIndexOf('/') + 1, url.length);

  const { data } = useRequest(() =>
    viewGeneralUsingPOST({ scanSensitiveId: Number(scanSensitiveId) }),
  );

  return (
    <PageContainer header={{ title: false }}>
      <div>
        <ProCard gutter={24} title="统计" wrap>
          {data?.result?.map((item: any, index: any) => (
            <ProCard
              style={{ marginBottom: 24 }}
              colSpan={colSpan}
              bordered
              hoverable
              key={index}
              onClick={() => {
                history.push(
                  `/data-desensitization/scan-sensitive/scan-sensitive-details/${scanSensitiveId}/${item.name}`,
                );
              }}
            >
              <Row gutter={16}>
                <Col>
                  <Title level={5} style={{ marginTop: '0' }}>
                    {item.name}
                  </Title>
                  <Space>
                    <Text type="secondary">总字段数:</Text>
                    <a>{item.totalFields}</a>
                    <Text type="secondary">敏感字段数:</Text>
                    <a>{item.sensitiveFields}</a>
                  </Space>
                </Col>
              </Row>
            </ProCard>
          ))}
        </ProCard>
      </div>
    </PageContainer>
  );
};

export default ScanSensitiveAnalysis;
