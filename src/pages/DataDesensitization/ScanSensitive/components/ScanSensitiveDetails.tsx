import { getAllRecognizeRuleUsingGET } from '@/services/dsp/recognizeRuleController';
import {
  getAllScanSensitiveInfoUsingGET,
  updateScanSensitiveInfoUsingPUT,
  viewDataUsingGET,
} from '@/services/dsp/scanSensitiveInfoController';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { useParams, useRequest } from '@@/exports';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useReactive } from 'ahooks';
import { Button, message, Modal, Select, Space, Table } from 'antd';
import React, { useRef, useState } from 'react';

interface ScanSensitiveInfoDetails extends API.ScanSensitiveInfo {
  ruleName: string;
}

const ScanSensitiveDetails: React.FC = () => {
  //详情页面
  const { id, table } = useParams();
  const tableRef = useRef<ActionType | undefined>();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const pagination = useReactive({
    total: 0,
    pageNum: 1,
    pageSize: 10,
    currentId: 0,
  });

  const { run: showModal, loading: showModalLoading } = useRequest(
    () =>
      viewDataUsingGET({
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        scanSensitiveInfoId: pagination.currentId as unknown as number,
      }),
    {
      manual: true,
      onSuccess: (data) => {
        pagination.total = data?.total || 0;
        setDataSource(data?.records?.map((name: string) => ({ name })));
        setIsModalOpen(true);
      },
    },
  );

  const { data: recognizeRuleData, loading: recognizeRuleLoading } = useRequest(
    () => getAllRecognizeRuleUsingGET({ pageNum: 1, pageSize: 10000 }),
    {
      onSuccess: () => {},
      formatResult: (res) => {
        return res?.data?.records;
      },
    },
  );

  const { run: submitRec, loading: recLoading } = useRequest(
    (value) => updateScanSensitiveInfoUsingPUT(value),
    {
      manual: true,
      onSuccess: () => {
        message.success('提交成功');
      },
    },
  );

  const columns: ProColumns<ScanSensitiveInfoDetails>[] = [
    {
      title: '项目空间',
      dataIndex: 'schemaName',
      width: 150,
    },
    {
      title: '表名',
      dataIndex: 'tableName',
    },
    {
      title: '列名',
      dataIndex: 'fieldName',
    },
    {
      title: '敏感字段类型',
      width: 200,
      render: (_, record) => {
        return (
          <Select
            onChange={(value: string | number) => {
              if (value || value === 0) {
                const formData = {
                  fieldName: record.fieldName,
                  id: record.id,
                  recognizeRuleId: Number(value),
                  scanSensitiveId: record.scanSensitiveId,
                  schemaName: record.schemaName,
                  status: record.status,
                  tableName: record.tableName,
                };
                submitRec(formData);
              }
            }}
            defaultValue={record.ruleName}
            style={{ width: 140 }}
            disabled={recLoading}
            loading={recognizeRuleLoading}
            options={recognizeRuleData}
            fieldNames={{
              value: 'id',
              label: 'ruleName',
            }}
          />
        );
      },
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              type="link"
              className="ant-btn-link-table"
              key="downloadExcel"
              onClick={() => {
                pagination.currentId = record.id as number;
                pagination.pageNum = 1;
                pagination.total = 0;
                showModal();
              }}
              loading={showModalLoading}
            >
              查看数据
            </Button>
          </Space>
        );
      },
    },
  ];

  const columns1 = [
    {
      title: '字段名',
      dataIndex: 'name',
      key: 'name',
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<ScanSensitiveInfoDetails>
        {...defaultTableConfig}
        actionRef={tableRef}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="详情列表"
        request={async (params) =>
          queryPagingTable<API.getAllScanSensitiveInfoUsingGETParams>(
            { ...params, scanSensitiveId: id, tableName: table },
            getAllScanSensitiveInfoUsingGET,
          )
        }
      />
      <Modal
        footer={null}
        onCancel={() => setIsModalOpen(false)}
        title="查看详情"
        open={isModalOpen}
      >
        <Table
          dataSource={dataSource}
          columns={columns1}
          size={'small'}
          pagination={{
            total: pagination.total,
            pageSize: pagination.pageSize,
            onChange: (page) => {
              pagination.pageNum = page;
              showModal();
            },
          }}
        />
      </Modal>
    </PageContainer>
  );
};

export default ScanSensitiveDetails;
