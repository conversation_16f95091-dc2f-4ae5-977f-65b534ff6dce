import { fetchSchemaAndTablesUsingGET } from '@/services/dsp/dbController';
import { RowEditableConfig } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { useReactive } from 'ahooks';
import { Input, Spin, Table } from 'antd';
import { SearchProps } from 'antd/es/input/Search';
import { useEffect, useRef, useState } from 'react';
const { Search } = Input;

type SchemaTableColumnProps = {
  dbId?: number;
  initCheckedKeys?: any;
  originCheckedKeys?: string[]; //采集配置中已选择的数组
  onSaveChange: (key?: string, tableSource?: any) => void;
  onCheckChange?: (checkedKeys?: any) => void;
};
type Strategy = { columnsName: string; codeName?: string; schema_tableName?: string };

export const getStrategyObj = (
  tableList: { columnsName: string; codeName?: string; schema_tableName?: string }[],
) => {
  let strategyObj: { [key: string]: string } = {};

  tableList?.forEach((item) => {
    strategyObj[item.columnsName] = item.codeName!;
  });
  return strategyObj;
};

const SchemaTableColumnTree: React.FC<RowEditableConfig<Strategy> & SchemaTableColumnProps> = ({
  dbId,
  originCheckedKeys,
  initCheckedKeys,
  onCheckChange,
}) => {
  const treeRef = useRef<
    {
      key: string;
      title: string;
      children?: {
        key: string;
        title: string;
        strategyList: { columnsName: string; codeName?: string }[];
      }[];
    }[]
  >([]);

  const checkedKeysRef = useRef<string>(initCheckedKeys || '');
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);

  const pagination = useReactive({
    total: 0,
    pageNum: 1,
    pageSize: 20,
    search: '',
  });

  //获取schema
  const { run: querySchemas, loading: schemaLoading } = useRequest(
    (dbId) =>
      fetchSchemaAndTablesUsingGET(
        {
          dbId,
          pageNum: pagination.pageNum,
          pageSize: pagination.pageSize,
          whereCluster: pagination.search,
        },
        { timeout: 5 * 60 * 1000 },
      ),
    {
      manual: true,
      onSuccess: (data) => {
        pagination.total = data?.total || 0;
        const schemas = data?.records?.map((item: { schema: string; table_name: string }) => ({
          key: `${item.schema}.${item.table_name}`,
          title: `${item.schema}.${item.table_name}`,
        }));
        treeRef.current = schemas;
        //筛选
        if (originCheckedKeys) {
          treeRef.current = schemas.filter((item: { key: string }) =>
            originCheckedKeys.includes(item.key),
          );
        }
      },
    },
  );
  useEffect(() => {
    pagination.pageNum = 1;
    pagination.total = 0;
    pagination.search = '';
    if (dbId) {
      querySchemas(dbId);
    }
  }, [dbId]);

  useEffect(() => {
    onCheckChange?.(checkedKeysRef.current);
    setCheckedKeys(checkedKeysRef.current?.split(','));
  }, [checkedKeysRef.current]);

  const onSearch: SearchProps['onSearch'] = (value) => {
    pagination.search = value;
    pagination.pageNum = 1;
    pagination.total = 0;
    querySchemas(dbId);
  };

  return (
    <Spin spinning={schemaLoading}>
      <Search placeholder="表名" onSearch={onSearch} style={{ marginBottom: '10px' }} />
      <Table
        pagination={{
          current: pagination.pageNum,
          total: pagination.total,
          pageSize: pagination.pageSize,
          onChange: (page, pageSize) => {
            pagination.pageNum = page;
            pagination.pageSize = pageSize;
            querySchemas(dbId);
          },
        }}
        scroll={{ y: 'calc(100vh - 280px)' }}
        columns={[
          {
            title: '表名',
            dataIndex: 'key',
            key: 'title',
          },
        ]}
        rowSelection={{
          type: 'checkbox',
          // hideSelectAll: true,
          selectedRowKeys: checkedKeys,
          onSelect: (record: any, checked) => {
            const value = record?.key;
            if (checked === false) {
              checkedKeysRef.current = checkedKeysRef.current
                ?.split(',')
                ?.filter((item) => item !== value)
                ?.join(',');
              setCheckedKeys(checkedKeysRef.current?.split(','));
            } else {
              checkedKeysRef.current = checkedKeysRef.current
                ? checkedKeysRef.current?.split(',')?.concat(value)?.join(',')
                : value;
              setCheckedKeys(checkedKeysRef.current?.split(','));
            }
          },
          onSelectAll: (checked, selectedRows, changeRows) => {
            const keys = changeRows?.map((item) => item?.key);
            if (checked === false) {
              checkedKeysRef.current = checkedKeysRef.current
                ?.split(',')
                ?.filter((item) => !keys.includes(item))
                ?.join(',');
              setCheckedKeys(checkedKeysRef.current?.split(','));
            } else {
              checkedKeysRef.current = checkedKeysRef.current
                ? checkedKeysRef.current?.split(',')?.concat(keys)?.join(',')
                : keys.join(',');
              setCheckedKeys(checkedKeysRef.current?.split(','));
            }
          },
        }}
        dataSource={treeRef.current}
      />
    </Spin>
  );
};

export default SchemaTableColumnTree;
