import BaseListContext from '@/components/Context/BaseListContext';
import { useListenerList } from '@/components/hooks/useListenerList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { USER_STATUS } from '@/enums';
import {
  batchDeleteUsingPOST2,
  execStrategyConfigUsingPOST,
  getAllUsingGET8,
} from '@/services/dsp/strategyConfigController';
import { option2enum, queryPagingTable, tableNamesTransform } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import DynamicDesensitizationModalForm from './components/DynamicDesensitizationDrawerForm';

const DynamicDesensitization: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.StrategyConfig[]>([]);
  const [initialValues, setInitialValues] = useState<API.StrategyConfig | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [rowId, setRowId] = useState<number>();
  const { listenerList, loading: listenerLoading } = useListenerList();

  const onEdit = (record: API.StrategyConfig) => {
    const { listenerId, tables } = record;
    const listenerItem = listenerList?.find((item: { id: number }) => item.id === listenerId);
    const { dbId, tableNames } = listenerItem;
    const tablesObj = tables && JSON.parse(tables);
    const initCheckedKeys = tables && tablesObj;
    const recordData = {
      ...record,
      dbId: dbId,
      originCheckedKeys: tableNamesTransform(tableNames),
      initCheckedKeys: initCheckedKeys,
    };
    setModalVisit(true);
    setInitialValues(recordData);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDeleteUsingPOST2(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = async (rows: API.StrategyConfig[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.configName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除脱敏“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  //禁用or启用
  const { run: disableRecord, loading } = useRequest((row) => execStrategyConfigUsingPOST(row), {
    manual: true,
    onSuccess: (data, params) => {
      const text = params.at(0).status === 0 ? '启用' : '禁用';
      message.success(`${text}成功`);
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDisabled = async (record: API.StrategyConfig) => {
    const text = record.status === 0 ? '禁用' : '启用';
    Modal.confirm({
      title: `确认“${text}”`,
      content: `您确定要${text}脱敏“${record.configName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        const status = record.status === 0 ? 1 : 0;
        const row = { ...record, status };
        disableRecord(row);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.StrategyConfig>[] = [
    {
      title: '名称',
      dataIndex: 'configName',
      fixed: 'left',
      width: 150,
      ellipsis: true,
    },
    {
      title: '采集配置',
      dataIndex: 'listenerName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '目标数据库',
      dataIndex: 'dbName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: option2enum(USER_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space>
            {record.status !== 0 && (
              <a key="edit" onClick={() => onEdit(record)}>
                编辑
              </a>
            )}
            <Button
              type="link"
              className="ant-btn-link-table"
              key="disabled"
              onClick={() => handleDisabled(record)}
              loading={loading && rowId === record.id}
            >
              {record.status === 0 ? '禁用' : '启动'}
            </Button>
            {record.status !== 0 && (
              <a key="del" onClick={() => handleDelete([record])}>
                删除
              </a>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.StrategyConfig>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: record?.status !== 1,
          }),
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="动态脱敏列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建脱敏
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUsingGET8Params>({ ...params }, getAllUsingGET8)
        }
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />

      <BaseListContext.Provider value={{ listenerList, listenerLoading }}>
        <DynamicDesensitizationModalForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default DynamicDesensitization;
