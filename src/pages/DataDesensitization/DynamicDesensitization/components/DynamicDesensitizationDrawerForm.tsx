import BaseListContext from '@/components/Context/BaseListContext';
import SchemaTableColumnTree from '@/components/Desensitization/SchemaTableColumnTree';
import { useDBList } from '@/components/hooks/useDBList';
import {
  addStrategyConfigUsingPOST,
  modifyStrategyConfigUsingPUT,
} from '@/services/dsp/strategyConfigController';
import { tableNamesTransform } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Divider, message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useContext, useRef } from 'react';

const DynamicDesensitizationDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  // 判断是否是新增
  const isEdit = initialValues?.id;
  const formRef = useRef<ProFormInstance>();
  //编辑时初始化已勾选的值
  const initCheckedKeys = initialValues?.initCheckedKeys;
  const tablesRef = useRef<{ [key: string]: { [key: string]: string } }>({});
  const checkedRef = useRef<{ [key: string]: { [key: string]: string } }>({});
  const { dbList, loading: dbListLoading } = useDBList();
  const { listenerList, listenerLoading } = useContext(BaseListContext);

  return (
    <DrawerForm
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={(val) => {
        onOpenChange?.(val);
      }}
      onFinish={async (value) => {
        const tableObj = Object.assign(checkedRef.current, tablesRef.current);
        const formData = {
          ...value,
          status: 1,
          tables: JSON.stringify(tableObj) ?? null,
        };

        const msg = isEdit
          ? await modifyStrategyConfigUsingPUT(formData)
          : await addStrategyConfigUsingPOST(formData);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
        <ProFormText name="dbId" label="dbId" placeholder="请输入" />
        <ProFormText name="originCheckedKeys" label="originCheckedKeys" placeholder="请输入" />
      </div>
      <ProFormText
        name="configName"
        label="名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
        disabled={isEdit}
      />
      <ProFormSelect
        name="listenerId"
        label="采集配置"
        rules={[requiredRule]}
        disabled={isEdit}
        options={listenerList as DefaultOptionType[]}
        fieldProps={{
          allowClear: false,
          loading: listenerLoading,
          fieldNames: {
            value: 'id',
            label: 'name',
          },
          onChange: (listenerId, option) => {
            const { dbId, tableNames } = option as API.DataListenerConfig;
            formRef.current?.setFieldValue('dbId', dbId);
            formRef.current?.setFieldValue('originCheckedKeys', tableNamesTransform(tableNames));
          },
        }}
      />
      <ProFormSelect
        name="targetDbId"
        label="目标数据库"
        rules={[requiredRule]}
        disabled={isEdit}
        fieldProps={{
          allowClear: false,
          loading: dbListLoading,
          showSearch: true,
          fieldNames: {
            value: 'id',
            label: 'dbName',
          },
        }}
        options={dbList as DefaultOptionType[]}
      />
      <ProFormDependency key="listenerId" name={['listenerId']} ignoreFormListField>
        {({ listenerId }) => {
          if (listenerId) {
            return (
              <>
                {/* 树型结构 */}
                <Divider type="horizontal" />
                <SchemaTableColumnTree
                  dbId={formRef.current?.getFieldValue('dbId')}
                  originCheckedKeys={formRef.current?.getFieldValue('originCheckedKeys')}
                  initCheckedKeys={initCheckedKeys}
                  onSaveChange={(key, dataSource) => {
                    tablesRef.current[key!] = dataSource;
                  }}
                  onCheckChange={(checked) => {
                    checkedRef.current = checked;
                  }}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default DynamicDesensitizationDrawerForm;
