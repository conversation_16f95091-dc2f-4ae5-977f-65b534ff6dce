import BaseListContext from '@/components/Context/BaseListContext';
import { useDBList } from '@/components/hooks/useDBList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { MANUAL_LAST_STATUS, MANUAL_STATUS } from '@/enums';
import {
  batchDelManualMaskUsingDELETE,
  executeManualMaskUsingPOST,
  getAllManualMaskUsingGET,
} from '@/services/dsp/manualMaskConfigController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import ManualDesensitizationDrawerForm, {
  getStrategyList,
} from './components/ManualDesensitizationDrawerForm';

const ManualDesensitization: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.ManualMaskConfig[]>([]);
  const [initialValues, setInitialValues] = useState<API.ManualMaskConfig | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [rowId, setRowId] = useState<number>();
  const { dbList, loading: dbLoading } = useDBList();

  const onEdit = (record: API.ManualMaskConfig) => {
    const { manualType, strategy, tables, dbId } = record;
    const tablesObj = tables && JSON.parse(tables);
    const dbListItem = dbList?.find((item: { id: number }) => item.id === dbId);
    const dbType = dbListItem?.dbType;
    const strategyObj = dbType !== 'MONGODB' && JSON.parse(strategy!);

    const initKeys = {};
    if (manualType === 'db') {
      if (dbType === 'MONGODB') {
        let checkedList: string[] = [];
        const halfList = Object.entries(tablesObj).map((item) => item[0]);
        Object.entries(tablesObj).forEach((item) => {
          const checkedArr = (item[1] as string[]).map((item1: string) => `${item[0]}.${item1}`);
          checkedList = [...checkedList, ...checkedArr];
        });
        Object.assign(initKeys, {
          checked: checkedList,
          halfChecked: halfList,
        });
      }
    }
    const recordData = {
      ...record,
      initKeys: dbType === 'MONGODB' && manualType === 'db' && initKeys,
      dbType: dbType,
      initCheckedKeys: tables && tablesObj,
      strategyList: ['file', 'sql'].includes(manualType!) && getStrategyList(strategyObj),
    };
    setModalVisit(true);
    setInitialValues(recordData);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDelManualMaskUsingDELETE(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = async (rows: API.ManualMaskConfig[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.maskName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除脱敏“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  //执行
  const { run: runRecord } = useRequest((row) => executeManualMaskUsingPOST(row), {
    manual: true,
    onSuccess: () => {
      message.success('操作成功');
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleRun = async (record: API.ManualMaskConfig) => {
    Modal.confirm({
      title: '确认执行',
      content: `您确定要执行脱敏“${record.maskName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        const row = { ...record, status: 0 };
        runRecord(row);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.ManualMaskConfig>[] = [
    {
      title: '名称',
      dataIndex: 'maskName',
      width: 150,
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'manualType',
      width: 150,
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      ellipsis: true,
    },
    {
      title: '最后执行时间',
      dataIndex: 'lastTime',
      width: 150,
      ellipsis: true,
    },
    {
      title: '当前状态',
      dataIndex: 'status',
      valueEnum: option2enum(MANUAL_STATUS),
      width: 150,
      ellipsis: true,
    },
    {
      title: '最后状态',
      dataIndex: 'lastStatus',
      valueEnum: option2enum(MANUAL_LAST_STATUS),
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 200,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="edit" onClick={() => onEdit(record)}>
              编辑
            </a>
            <Button
              type="link"
              className="ant-btn-link-table"
              key="run"
              onClick={() => handleRun(record)}
              loading={rowId === record.id || record.status === 0}
              disabled={record.status === 0}
            >
              执行
            </Button>
            <a
              key="view"
              onClick={() => {
                history.push(
                  `/data-desensitization/manual-desensitization/manual-desensitization-details/${record.id}`,
                );
              }}
            >
              详情
            </a>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.ManualMaskConfig>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: record?.status !== 1,
          }),
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="手动脱敏列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建脱敏
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllManualMaskUsingGETParams>(
            { ...params },
            getAllManualMaskUsingGET,
          )
        }
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <BaseListContext.Provider value={{ dbList, dbLoading }}>
        <ManualDesensitizationDrawerForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default ManualDesensitization;
