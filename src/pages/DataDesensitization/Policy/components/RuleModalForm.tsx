import { MASK_RULE_TYPES } from '@/enums';
import {
  addRecognizeRuleUsingPOST,
  modifyRecognizeRuleUsingPOST,
} from '@/services/dsp/recognizeRuleController';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormInstance,
  ProFormRadio,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';

const RuleModalForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  // 判断是否是新增
  const isEdit = !!initialValues?.id;
  const formRef = useRef<ProFormInstance>();

  return (
    <DrawerForm
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={(val) => onOpenChange?.(val)}
      onFinish={async (value) => {
        const formData = {
          ...value,
        };
        const msg = isEdit
          ? await modifyRecognizeRuleUsingPOST(formData)
          : await addRecognizeRuleUsingPOST({ ...formData, status: 0 });
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={isEdit ? initialValues : { defaultMaskType: 'COVER', isNumber: '0' }}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <div style={{ display: 'none' }}>
        <ProFormText name="status" label="status" placeholder="请输入" />
      </div>
      <ProFormText
        name="ruleName"
        label="名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormText
        name="contentLength"
        label="长度"
        placeholder="使用逗号隔开多个"
        fieldProps={{
          autoComplete: 'none',
        }}
      />
      <ProFormText
        name="reg"
        label="正则表达式"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
      />
      <ProFormRadio.Group
        name="defaultMaskType"
        label="默认脱敏规则"
        options={MASK_RULE_TYPES.filter((item) => item.value !== 'SIMULATE').map((i) => {
          if (i.value === 'ROUND') {
            i.label = '金额脱敏';
          }
          return { ...i };
        })}
        rules={[requiredRule]}
      />
      <ProFormRadio.Group
        name="isNumber"
        label="是否为数字"
        options={[
          { label: '是', value: '1' },
          { label: '否', value: '0' },
        ]}
        rules={[requiredRule]}
      />
      <ProFormTextArea
        name="description"
        label="说明"
        placeholder="请输入"
        fieldProps={{ autoSize: { minRows: 2, maxRows: 3 } }}
      />
    </DrawerForm>
  );
};

export default RuleModalForm;
