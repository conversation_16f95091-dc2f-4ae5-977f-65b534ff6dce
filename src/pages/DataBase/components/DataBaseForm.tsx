import RKCol from '@/components/RKCol';
import { ENVIRONMENT_TYPE } from '@/enums';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import {
  addDbUsingPOST,
  dbTestUsingPOST,
  getDbTypeUsingGET,
  modifyDbUsingPUT,
  searchDbByIdUsingGET,
} from '@/services/dsp/dbController';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import { IPReg, PortReg, RacNodeReg } from '@/utils/validator';
import {
  FooterToolbar,
  PageContainer,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Row } from 'antd';
import { useRef } from 'react';

const DataBaseForm: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  //数据库类型
  const { data: dbTypeList = [], loading: dbTypeLoading } = useRequest(() => getDbTypeUsingGET());

  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => addDbUsingPOST(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: edit, loading: editLoading } = useRequest((value) => modifyDbUsingPUT(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  //连接测试
  const {
    run: linkTest,
    loading: linkLoading,
    data: linkCode,
  } = useRequest((value) => dbTestUsingPOST(value), {
    manual: true,
    formatResult: (res) => {
      const { code } = res;
      if (code !== 100) return;
      message.success('连接成功');
      return code;
    },
  });
  const isDisabled = linkCode !== 100;

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <ProForm
        formRef={formRef}
        submitter={{
          searchConfig: {
            resetText: '取消',
            submitText: '保存',
          },
          onReset: () => {
            history.go(-1);
          },
          //自定义整个区域
          render: (props, doms) => {
            return (
              <FooterToolbar>
                {doms?.[0]}
                <Button
                  type="primary"
                  key="linkTest"
                  onClick={async () => {
                    const formData =
                      (await formRef.current?.validateFieldsReturnFormatValue?.()) || {};
                    linkTest({ ...formData });
                  }}
                  loading={linkLoading}
                >
                  测试连接
                </Button>
                {doms?.[1]}
              </FooterToolbar>
            );
          },
          submitButtonProps: {
            loading: addLoading || editLoading,
            disabled: isDisabled ?? true,
          },
          resetButtonProps: {
            style: {
              marginLeft: 8,
            },
          },
        }}
        onFinish={async (value) => {
          if (isEditPage) {
            edit(value);
          } else {
            add(value);
          }
        }}
        request={async () => queryFormData({ id: id }, isEditPage, searchDbByIdUsingGET)}
      >
        {/* 不需要展示，只是为了form传值 */}
        <div style={{ display: 'none' }}>
          <ProFormText name="id" label="id" placeholder="请输入" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="dbName" label="名称" placeholder="请输入" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="dbType"
              label="数据库类型"
              options={dbTypeList as string[]}
              rules={[requiredRule]}
              showSearch
              fieldProps={{ loading: dbTypeLoading }}
            />
          </RKCol>
          <RKCol>
            <ProFormSelect
              name="enType"
              label="环境类型"
              options={ENVIRONMENT_TYPE}
              rules={[requiredRule]}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="host"
              label="主机"
              placeholder="请输入"
              rules={[
                requiredRule,
                {
                  pattern: IPReg,
                  message: '主机地址必须有四段，段与段之间用点分开',
                },
              ]}
            />
          </RKCol>
          <RKCol>
            <ProFormText
              name="port"
              label="端口"
              placeholder="请输入"
              rules={[
                requiredRule,
                {
                  pattern: PortReg,
                  message: '端口必须是数字',
                },
              ]}
            />
          </RKCol>
          <ProFormDependency name={['dbType']}>
            {({ dbType }) => {
              const show = dbType !== 'MONGODB';
              return (
                <>
                  <RKCol>
                    <ProFormText
                      name="seName"
                      label={show ? '服务名' : '数据库名'}
                      placeholder="请输入"
                      rules={[requiredRule]}
                    />
                  </RKCol>
                  {show === true && (
                    <RKCol>
                      <ProFormText name="pdbName" label="PDB名称" placeholder="请输入" />
                    </RKCol>
                  )}
                  <RKCol>
                    <ProFormText
                      name="dbUser"
                      label="用户名"
                      placeholder="请输入"
                      fieldProps={{
                        autoComplete: 'none',
                      }}
                      rules={[
                        {
                          required: show,
                          message: '用户名为必填项',
                        },
                      ]}
                    />
                  </RKCol>
                  <RKCol>
                    <ProFormText.Password
                      name="dbPassword"
                      label="密码"
                      rules={[
                        {
                          required: show,
                          message: '密码为必填项',
                        },
                      ]}
                      fieldProps={{
                        autoComplete: 'new-password',
                      }}
                    />
                  </RKCol>
                  {show === true && (
                    <RKCol lg={12} md={16} sm={24}>
                      <ProFormTextArea
                        name="racNodes"
                        label="RAC设置"
                        rules={[
                          {
                            pattern: RacNodeReg,
                            message: '格式必须为IP或者IP+端口,多个IP请用,分割',
                          },
                        ]}
                        fieldProps={{
                          autoSize: {
                            minRows: 1,
                            maxRows: 2,
                          },
                        }}
                      />
                    </RKCol>
                  )}
                </>
              );
            }}
          </ProFormDependency>
        </Row>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(DataBaseForm);
