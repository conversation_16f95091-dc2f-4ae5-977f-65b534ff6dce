import { EXECUTION_STATUS } from '@/enums';
import { getAllUsingGET5 } from '@/services/dsp/logController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef } from 'react';

const Users: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();

  // 表格
  const columns: ProColumns<DSP_API.SysLog>[] = [
    {
      title: '用户名',
      dataIndex: 'userName',
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作类型',
      dataIndex: 'operation',
      width: 100,
      ellipsis: true,
    },
    {
      title: '调用方法',
      dataIndex: 'servletPath',
      width: 180,
      ellipsis: true,
    },
    {
      title: '执行结果',
      dataIndex: 'operatStatus',
      width: 100,
      valueEnum: option2enum(EXECUTION_STATUS),
    },
    {
      title: '执行参数',
      dataIndex: 'parameters',
      width: 200,
      ellipsis: true,
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      width: 200,
      ellipsis: true,
    },
    {
      title: '执行时间',
      dataIndex: 'createTime',
      width: 180,
      ellipsis: true,
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<DSP_API.SysLog>
        {...defaultTableConfig}
        actionRef={tableRef}
        columns={columns}
        headerTitle="日志列表"
        request={async (params) =>
          queryPagingTable<API.getAllUsingGET5Params>({ ...params }, getAllUsingGET5)
        }
      />
    </PageContainer>
  );
};

export default Users;
