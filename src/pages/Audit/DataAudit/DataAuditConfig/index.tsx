import BaseListContext from '@/components/Context/BaseListContext';
import { useListenerList } from '@/components/hooks/useListenerList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { EXEC_STATUS } from '@/enums';
import {
  batchDeleteUsingPOST,
  execAuditUsingPOST,
  getAllUsingGET,
} from '@/services/dsp/auditConfigController';
import { option2enum, queryPagingTable, tableNamesTransform, tablesTransform } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import React, { useRef, useState } from 'react';
import DataAuditConfigDrawerForm from './components/DataAuditConfigDrawerForm';

const AuditConfigs: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.AuditConfig[]>([]);
  const [initialValues, setInitialValues] = useState<API.AuditConfig | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [rowId, setRowId] = useState<number>();
  const { listenerList, loading: listenerLoading } = useListenerList();

  const onEdit = (record: API.AuditConfig) => {
    const listenerItem = listenerList?.find(
      (item: { id: number }) => item.id === record.listenerId,
    );
    const { tableNames, dbId } = listenerItem;
    const { tables } = record;
    const recordData = {
      ...record,
      initCheckedKeys: tablesTransform(tables),
      originCheckedKeys: tableNamesTransform(tableNames),
      dbId: dbId,
    };
    setModalVisit(true);
    setInitialValues(recordData);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDeleteUsingPOST(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = async (rows: API.AuditConfig[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除配置“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };
  //停止or启动
  const { run: disableRecord, loading } = useRequest((row) => execAuditUsingPOST(row), {
    manual: true,
    onSuccess: (data, params) => {
      const text = params.at(0).status === 0 ? '启动' : '停止';
      message.success(`${text}成功`);
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDisabled = async (record: API.AuditConfig) => {
    const text = record.status === 0 ? '停止' : '启动';
    Modal.confirm({
      title: `确认“${text}”`,
      content: `您确定要${text}配置“${record.name}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        const status = record.status === 0 ? 1 : 0;
        const row = { ...record, status };
        disableRecord(row);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.AuditConfig>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 150,
      ellipsis: true,
    },
    {
      title: '采集配置名称',
      dataIndex: 'listenerName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '数据库名称',
      dataIndex: 'dbName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '数据库类型',
      dataIndex: 'dbType',
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作时间',
      width: 160,
      ellipsis: true,
      dataIndex: 'createTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      ellipsis: true,
      valueEnum: option2enum(EXEC_STATUS),
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            {record.status !== 0 && (
              <a key="edit" onClick={() => onEdit(record)}>
                编辑
              </a>
            )}
            <Button
              type="link"
              className="ant-btn-link-table"
              key="disabled"
              onClick={() => handleDisabled(record)}
              loading={loading && rowId === record.id}
            >
              {record.status === 0 ? '停止' : '启动'}
            </Button>
            {record.status !== 0 && (
              <a key="del" onClick={() => handleDelete([record])}>
                删除
              </a>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.AuditConfig>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: record?.status !== 1,
          }),
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="配置列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建配置
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUsingGETParams>({ ...params }, getAllUsingGET)
        }
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <BaseListContext.Provider value={{ listenerList, listenerLoading }}>
        <DataAuditConfigDrawerForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default AuditConfigs;
