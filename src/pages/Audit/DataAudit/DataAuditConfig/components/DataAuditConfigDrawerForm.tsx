import BaseListContext from '@/components/Context/BaseListContext';
import SchemaTable from '@/components/SchemaTableTree/index';
import { addAuditUsingPOST, modifyAuditConfigUsingPUT } from '@/services/dsp/auditConfigController';
import { tableNamesTransform } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Divider, message, TreeProps } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useContext, useRef } from 'react';

const DataAuditConfigDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.id;
  const initCheckedKeys = initialValues?.initCheckedKeys;
  const formRef = useRef<ProFormInstance>();
  const tableNamesRef = useRef<string[]>([]);
  const { listenerList, listenerLoading } = useContext(BaseListContext);

  return (
    <DrawerForm<API.DataListenerConfig>
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={(val) => {
        onOpenChange?.(val);
      }}
      onFinish={async (value) => {
        let tables = '';
        if (tableNamesRef.current.length > 0) {
          tables = tableNamesRef.current.toString();
        } else {
          tables = initialValues?.tables;
        }
        const formData = {
          ...value,
          status: isEdit ? initialValues?.status : 1,
          tables: tables,
        };
        const msg = isEdit
          ? await modifyAuditConfigUsingPUT(formData)
          : await addAuditUsingPOST(formData);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
        <ProFormText name="originCheckedKeys" label="originCheckedKeys" />
        <ProFormText name="dbId" label="dbId" />
      </div>
      <ProFormText
        name="name"
        label="名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
        disabled={isEdit}
      />
      <ProFormSelect
        name="listenerId"
        label="采集配置"
        rules={[requiredRule]}
        options={listenerList as DefaultOptionType[]}
        fieldProps={{
          allowClear: false,
          loading: listenerLoading,
          fieldNames: {
            value: 'id',
            label: 'name',
          },
          onChange: (listenerId, option) => {
            const { dbId, tableNames } = option as API.DataListenerConfig;
            formRef.current?.setFieldValue('dbId', dbId);
            const tableNameList: string[] = tableNames?.split(',') || [];
            formRef.current?.setFieldValue('originCheckedKeys', tableNamesTransform(tableNames));
            tableNamesRef.current = tableNameList; //若没有重新选择则使用已存在的tableNames
          },
        }}
        disabled={isEdit}
      />
      {/* 树型结构 */}
      <ProFormDependency key="listenerId" name={['listenerId']} ignoreFormListField>
        {({ listenerId }) => {
          if (listenerId) {
            return (
              <>
                <Divider type="horizontal"></Divider>
                <SchemaTable
                  isShowSearch={false}
                  dbId={formRef.current?.getFieldValue('dbId')}
                  initCheckedKeys={initCheckedKeys}
                  originCheckedKeys={formRef.current?.getFieldValue('originCheckedKeys')}
                  onCheck={(values: TreeProps['checkedKeys']) => {
                    tableNamesRef.current = (
                      values as {
                        checked: string[];
                        halfChecked: string[];
                      }
                    ).checked?.filter((item) => (item as string).indexOf('.') !== -1);
                  }}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default DataAuditConfigDrawerForm;
