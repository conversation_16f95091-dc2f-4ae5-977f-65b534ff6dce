import { getAllUsingGET } from '@/services/dsp/auditConfigController';
import { getAllUsingPOST } from '@/services/dsp/auditDataController';
import { formatDate } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Space } from 'antd';
import React, { useRef, useState } from 'react';
import ChangeDetailDrawerForm from './components/ChangeDetailDrawerForm';

const DataAuditJournal: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const tableRef = useRef<ActionType | undefined>();
  const [initialValues, setInitialValues] = useState<API.AuditData | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const { data: auditConfigData = {}, loading: auditConfigLoading } = useRequest(() =>
    getAllUsingGET({ pageNum: 1, pageSize: 9999 }),
  );
  const auditConfigList = auditConfigData?.records?.map((item: Record<string, any>) => ({
    ...item,
    label: item.name,
    value: item.id.toString(),
  }));

  const onChangeDetail = (record: API.AuditData) => {
    setModalVisit(true);
    setInitialValues(record);
  };

  // 表格
  const columns: ProColumns<API.AuditData>[] = [
    {
      title: '审计配置',
      dataIndex: 'AUDITCONFIGID',
      valueType: 'select',
      fieldProps: () => ({
        allowClear: false,
        showSearch: true,
        loading: auditConfigLoading,
        options: auditConfigList,
      }),
      hideInTable: true,
      formItemProps: {
        rules: [requiredRule],
      },
    },
    {
      title: '类型',
      dataIndex: 'AUDITTYPE',
      width: 100,
      valueType: 'select',
      valueEnum: {
        INSERT: { text: 'INSERT' },
        DELETE: { text: 'DELETE' },
        UPDATE: { text: 'UPDATE' },
      },
      hideInTable: true,
    },
    {
      title: '用户',
      dataIndex: 'USERNAME',
      hideInTable: true,
    },
    {
      title: '表名',
      dataIndex: 'TABLENAME',
      hideInTable: true,
    },
    {
      title: '操作时间',
      dataIndex: 'AUDITTIME',
      renderText: formatDate,
      valueType: 'dateTimeRange',
      search: {
        transform: (value: any) => ({
          auditTime: value?.[0] + '|' + value?.[1],
        }),
      },
      hideInTable: true,
    },
    {
      title: '审计配置',
      width: 100,
      fixed: 'left',
      ellipsis: true,
      dataIndex: 'auditConfigId',
      valueType: 'select',
      fieldProps: () => ({
        loading: auditConfigLoading,
        options: auditConfigList,
      }),
      search: false,
    },
    {
      title: '类型',
      dataIndex: 'auditType',
      width: 100,
      ellipsis: true,
      valueType: 'select',
      valueEnum: {
        INSERT: { text: 'INSERT' },
        DELETE: { text: 'DELETE' },
        UPDATE: { text: 'UPDATE' },
      },
      search: false,
    },
    {
      title: '用户',
      dataIndex: 'userName',
      search: false,
      width: 100,
      ellipsis: true,
    },
    {
      title: '表名',
      dataIndex: 'tableName',
      search: false,
      width: 180,
      ellipsis: true,
    },
    {
      title: 'ddl',
      search: false,
      dataIndex: 'ddlStr',
      width: 180,
      ellipsis: true,
    },
    {
      title: '操作时间',
      dataIndex: 'auditTime',
      renderText: formatDate,
      width: 150,
      search: false,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <>
              {!record.ddlStr && (
                <a key="change_details" onClick={() => onChangeDetail(record)}>
                  变更详情
                </a>
              )}
              <a
                key="details"
                onClick={() =>
                  history.push('/audit/data-audit/data-audit-journal/data-audit-journal-detail', {
                    ...record,
                  })
                }
              >
                详情
              </a>
            </>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.AuditData>
        {...defaultTableConfig}
        form={{
          ignoreRules: false,
          syncToUrl: true,
          syncToInitialValues: false,
        }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        formRef={formRef}
        actionRef={tableRef}
        columns={columns}
        headerTitle="日志列表"
        request={async (params) => {
          const { AUDITCONFIGID, AUDITTYPE, USERNAME, TABLENAME, AUDITTIME, current, pageSize } =
            params;
          const search = {
            AUDITCONFIGID: Number(AUDITCONFIGID),
            AUDITTYPE,
            USERNAME,
            TABLENAME,
            AUDITTIME,
          };
          const pageData = { pageNum: current, pageSize: pageSize };
          if (AUDITCONFIGID) {
            const msg = await getAllUsingPOST(pageData, search);
            const success = msg.code === 100;
            if (success) {
              return {
                data: msg?.data?.records || [],
                success: true,
                total: msg?.data?.total,
              };
            }
          }
          return { success: true };
        }}
        polling={5000}
      />
      <ChangeDetailDrawerForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
      />
    </PageContainer>
  );
};

export default DataAuditJournal;
