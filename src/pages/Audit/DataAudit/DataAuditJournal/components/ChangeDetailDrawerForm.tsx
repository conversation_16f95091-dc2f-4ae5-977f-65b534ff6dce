import {
  DrawerForm,
  DrawerFormProps,
  ProFormField,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { Descriptions } from 'antd';
import React, { useRef } from 'react';
import style from './index.less';

const ChangeDetailDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
}) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <DrawerForm<API.AuditData>
      width={460}
      title="变更详情"
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      submitter={false}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
      className={style.descriptionStyle}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <Descriptions layout="vertical" column={1} bordered>
        <Descriptions.Item label="变更前" labelStyle={{ fontSize: '17px', fontWeight: 'bold' }}>
          <div style={{ backgroundColor: initialValues?.beforeData && 'rgb(246, 248, 250)' }}>
            <ProFormField valueType="jsonCode" mode="read" text={initialValues?.beforeData} />
          </div>
        </Descriptions.Item>
        <Descriptions.Item label="变更后" labelStyle={{ fontSize: '17px', fontWeight: 'bold' }}>
          <div style={{ backgroundColor: initialValues?.afterData && 'rgb(246, 248, 250)' }}>
            <ProFormField valueType="jsonCode" mode="read" text={initialValues?.afterData} />
          </div>
        </Descriptions.Item>
      </Descriptions>
    </DrawerForm>
  );
};

export default ChangeDetailDrawerForm;
