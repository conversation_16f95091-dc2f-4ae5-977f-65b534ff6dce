import { getDetailUsingPOST } from '@/services/dsp/auditDataController';
import { editPageData } from '@/utils';
import { PageContainer, ProFormField } from '@ant-design/pro-components';
import { useLocation, useRequest } from '@umijs/max';
import { Descriptions, Timeline } from 'antd';
import style from './index.less';

//数据审计生命周期
const DataAuditJournalDetail: React.FC = () => {
  const location = useLocation();
  const locationState = location.state as API.AuditData;
  const detailData = editPageData(locationState);
  const { data = [] } = useRequest(() => getDetailUsingPOST(detailData));
  const getCircle = (auditType: string) => {
    let type = 'C';
    if (auditType !== 'INSERT') {
      type = auditType.charAt(0).toUpperCase();
    }
    return (
      <div className={style['circle']}>
        <div style={{ fontSize: '16px' }}>{type}</div>
      </div>
    );
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <div style={{ padding: '16px 24px', backgroundColor: '#fff' }}>
        <Timeline style={{ margin: '10px 200px' }}>
          {data?.map(
            (item: {
              id: number;
              auditTime: string;
              auditType: string;
              ddlStr: string;
              afterData: string;
              beforeData: string;
            }) => {
              return (
                <div key={item.id}>
                  <Timeline.Item label={item.auditTime} dot={getCircle(item.auditType)}>
                    {!item.ddlStr ? (
                      <div>
                        <Descriptions layout="vertical" bordered column={1}>
                          {
                            <>
                              {item.beforeData && (
                                <Descriptions.Item
                                  label="变更前"
                                  labelStyle={{ fontSize: '17px', fontWeight: 'bold' }}
                                >
                                  <div style={{ backgroundColor: 'rgb(246, 248, 250)' }}>
                                    <ProFormField
                                      valueType="jsonCode"
                                      mode="read"
                                      text={item.beforeData}
                                    />
                                  </div>
                                </Descriptions.Item>
                              )}
                              {item.afterData && (
                                <Descriptions.Item
                                  label="变更后"
                                  labelStyle={{ fontSize: '17px', fontWeight: 'bold' }}
                                >
                                  <div style={{ backgroundColor: 'rgb(246, 248, 250)' }}>
                                    <ProFormField
                                      valueType="jsonCode"
                                      mode="read"
                                      text={item.afterData}
                                    />
                                  </div>
                                </Descriptions.Item>
                              )}
                            </>
                          }
                        </Descriptions>
                      </div>
                    ) : (
                      <div>
                        <Descriptions layout="vertical" bordered size="small" column={1}>
                          {item.ddlStr && (
                            <Descriptions.Item
                              label="ddl"
                              labelStyle={{ fontSize: '17px', fontWeight: 'bold' }}
                            >
                              <div style={{ backgroundColor: 'rgb(246, 248, 250)' }}>
                                <ProFormField valueType="jsonCode" mode="read" text={item.ddlStr} />
                              </div>
                            </Descriptions.Item>
                          )}
                        </Descriptions>
                      </div>
                    )}
                  </Timeline.Item>
                </div>
              );
            },
          )}
        </Timeline>
      </div>
    </PageContainer>
  );
};

export default DataAuditJournalDetail;
