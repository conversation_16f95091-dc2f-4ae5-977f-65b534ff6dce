.circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin: 0;
  padding: 0;
  color: white;
  text-align: center;
  background-color: #36cfc9;
  border-radius: 50%;
}

.descriptionStyle {
  :global {
    .ant-descriptions.ant-descriptions-bordered .ant-descriptions-view {
      border: 1px solid transparent;
      table {
        border: 1px solid rgba(5, 5, 5, 0.06);
      }
    }
  }
}
