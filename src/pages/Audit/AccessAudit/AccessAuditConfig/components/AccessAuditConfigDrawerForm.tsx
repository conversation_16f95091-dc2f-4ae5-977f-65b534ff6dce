import { useDBList } from '@/components/hooks/useDBList';
import { addScheduleUsingPOST, updateUsingPOST } from '@/services/dsp/scheduledJobController';
import { requiredRule } from '@/utils/setting';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  DrawerFormProps,
  ModalForm,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import Tooltip from 'antd/lib/tooltip';
import dayjs, { ManipulateType } from 'dayjs';
import React, { useCallback, useRef, useState } from 'react';
import styles from './index.less';

//访问审计配置
const generateTime = (
  startTime: string,
  frequency: number,
  frequencyUnit: ManipulateType | 'oneTime',
  count: number = 10,
) => {
  const result = [];
  let current = dayjs(startTime);
  // 如果只有一次
  if (frequencyUnit === 'oneTime') {
    result.push(current.format('YYYY-MM-DD HH:mm:ss'));
    return result;
  }
  for (let i = 0; i < count; i++) {
    result.push(current.format('YYYY-MM-DD HH:mm:ss'));
    current = current.add(frequency, frequencyUnit);
  }
  return result;
};

export const frequencyOptions = [
  {
    value: 'minute',
    label: '分钟',
  },
  {
    value: 'hour',
    label: '小时',
  },
  {
    value: 'day',
    label: '日',
  },
];

const AccessAuditConfigDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.jobId;
  const formRef = useRef<ProFormInstance>();
  const [dateArr, setDateArr] = useState<string[]>([]);
  const { dbList, loading: dbListLoading } = useDBList();

  const getTime = () => {
    const { frequency: frequencyUnit, intervals = 0 } = formRef.current?.getFieldsValue();
    if (!frequencyUnit) {
      message.error('请先选择重复频率');
      return;
    }
    const startTime = dayjs(`${new Date()}`).format('YYYY-MM-DD HH:mm:ss');
    const arr = generateTime(startTime, intervals, frequencyUnit);
    setDateArr(arr);
  };
  const Descriptions = useCallback(
    () => (
      <>
        <span>计划描述</span>
        <Tooltip title="点击生成，查看计划描述">
          <QuestionCircleOutlined
            style={{ color: 'rgba(0, 0, 0, 0.45)', margin: '0 10px 0 4px' }}
          />
        </Tooltip>
        <a onClick={getTime}>生成</a>
      </>
    ),
    [],
  );

  //获得corn表达式 若是为最后一天呢?
  const getCron = (frequency: string, intervals: string) => {
    const seconds = dayjs(`${new Date()}`).second(); //秒
    const minutes = dayjs(`${new Date()}`).minute(); //分钟
    const hours = dayjs(`${new Date()}`).hour(); //小时
    let corn: string = '';
    switch (frequency) {
      case 'minute':
        corn = `${seconds} */${intervals} * * * ? `;
        break;
      case 'hour':
        corn = `${seconds} ${minutes} */${intervals} * * ? `;
        break;
      case 'day':
        corn = `${seconds} ${minutes} ${hours} */${intervals} * ? `;
        break;
    }
    return corn;
  };

  return (
    <ModalForm
      width={736}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={(val) => {
        setDateArr([]);
        onOpenChange?.(val);
      }}
      onFinish={async (value) => {
        const { frequency, intervals } = value;
        const corns = getCron(frequency, intervals);
        const formData = {
          ...value,
          status: isEdit ? initialValues?.status : 0,
          cronExpression: corns,
        };
        const msg = isEdit ? await updateUsingPOST(formData) : await addScheduleUsingPOST(formData);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      modalProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="jobId" label="jobId" placeholder="请输入" />
      </div>
      <ProForm.Group>
        <ProFormText
          name="jobName"
          width="md"
          label="名称"
          placeholder="请输入"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
          disabled={isEdit}
        />
        <ProFormSelect
          name="dbId"
          width="md"
          label="数据库配置"
          allowClear={false}
          rules={[requiredRule]}
          fieldProps={{
            loading: dbListLoading,
            showSearch: true,
            fieldNames: {
              value: 'id',
              label: 'dbName',
            },
          }}
          options={dbList as DefaultOptionType[]}
        />
      </ProForm.Group>

      <ProForm.Group>
        <ProFormSelect
          name="frequency"
          width={127}
          options={frequencyOptions}
          label="重复频率"
          allowClear={false}
          rules={[requiredRule]}
        />
        <ProFormDigit
          width={168}
          name="intervals"
          label="每"
          placeholder="请输入"
          fieldProps={{ precision: 0 }}
          min={1}
          max={9999}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (getFieldValue('frequency') !== 'oneTime' && !value) {
                  return Promise.reject(new Error('这是必填项'));
                }
                return Promise.resolve();
              },
            }),
          ]}
        />
      </ProForm.Group>
      <Descriptions />
      <div className={styles.info}>
        {dateArr.map((item, index) => (
          <span key={index}> {item}</span>
        ))}
      </div>
    </ModalForm>
  );
};

export default AccessAuditConfigDrawerForm;
