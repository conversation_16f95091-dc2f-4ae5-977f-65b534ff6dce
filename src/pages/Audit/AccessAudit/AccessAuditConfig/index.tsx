import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import { EXEC_STATUS } from '@/enums';
import {
  delScheduleUsingDELETE,
  getAllUsingGET7,
  startScheduleUsingPOST,
  stopScheduleUsingPOST,
} from '@/services/dsp/scheduledJobController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import AccessAuditConfigDrawerForm from './components/AccessAuditConfigDrawerForm';

const AccessAuditConfig: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [initialValues, setInitialValues] = useState<API.ScheduledJob | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [rowId, setRowId] = useState<number>();

  const onEdit = (record: API.ScheduledJob) => {
    const { cronExpression } = record;
    const intervals = cronExpression
      ?.substring(cronExpression.indexOf('/') + 1, cronExpression.length)
      .split(' ')
      .at(0);
    let frequency: string = '';
    cronExpression?.split(' ').forEach((item: string, index: any) => {
      if (item.indexOf('/') !== -1) {
        switch (index) {
          case 1:
            frequency = 'minute';
            break;
          case 2:
            frequency = 'hour';
            break;
          case 3:
            frequency = 'day';
            break;
        }
      }
    });
    const data = { ...record, frequency: frequency, intervals: intervals };
    setModalVisit(true);
    setInitialValues(data);
  };

  // 删除
  const { run: deleteRecord } = useRequest(
    (scheduledJobId) => delScheduleUsingDELETE({ scheduledJobId }),
    {
      manual: true,
      onSuccess: () => {
        message.success('删除成功');
        tableRef.current?.reloadAndRest?.();
      },
    },
  );
  const handleDelete = async (row: API.ScheduledJob) => {
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除配置“${row.jobName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(row.jobId!);
      },
    });
  };
  //停止
  const { run: disableRecord } = useRequest((row) => stopScheduleUsingPOST(row), {
    manual: true,
    onSuccess: () => {
      message.success('停止成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  //启动
  const { run: startRecord, loading } = useRequest((row) => startScheduleUsingPOST(row), {
    manual: true,
    onSuccess: () => {
      message.success('启动成功');
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDisabled = async (record: API.ScheduledJob) => {
    const text = record.status === 0 ? '停止' : '启动';
    Modal.confirm({
      title: `确认“${text}”`,
      content: `您确定要${text}配置“${record.jobName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.jobId);
        const status = record.status === 0 ? 1 : 0;
        const row = { ...record, status };
        if (record.status === 0) {
          disableRecord(row);
        } else {
          startRecord(row);
        }
      },
    });
  };

  // 表格
  const columns: ProColumns<API.ScheduledJob>[] = [
    {
      title: '名称',
      dataIndex: 'jobName',
      fixed: 'left',
      width: 150,
      ellipsis: true,
    },
    {
      title: '数据库名称',
      dataIndex: 'dbName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '数据库类型',
      dataIndex: 'dbType',
      width: 120,
      ellipsis: true,
    },
    {
      title: '执行周期',
      dataIndex: 'cronStr',
      width: 100,
      ellipsis: true,
    },

    {
      title: '操作时间',
      dataIndex: 'createTime',
      width: 150,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: option2enum(EXEC_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a key="edit" onClick={() => onEdit(record)}>
              编辑
            </a>
            <Button
              type="link"
              className="ant-btn-link-table"
              key="disabled"
              onClick={() => handleDisabled(record)}
              loading={loading && rowId === record.jobId}
            >
              {record.status === 0 ? '停止' : '启动'}
            </Button>
            <a key="del" onClick={() => handleDelete(record)}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.ScheduledJob>
        {...defaultTableConfig}
        rowKey="jobId"
        actionRef={tableRef}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="配置列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建配置
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUsingGET7Params>({ ...params }, getAllUsingGET7)
        }
        polling={5000}
      />
      {/* 新建/编辑模版 */}
      <AccessAuditConfigDrawerForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default AccessAuditConfig;
