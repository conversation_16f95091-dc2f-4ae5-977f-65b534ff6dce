import { useDBList } from '@/components/hooks/useDBList';
import { getAllUsingPOST1 } from '@/services/dsp/auditLoginController';
import { formatDate } from '@/utils';
import { defaultTableConfig, requiredRule } from '@/utils/setting';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import React, { useEffect, useRef } from 'react';

const AccessAuditJournal: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const tableRef = useRef<ActionType | undefined>();
  const { dbList, loading: dbListLoading } = useDBList();

  useEffect(() => {
    formRef.current?.setFieldValue('dbId', dbList[0]?.id);
    formRef.current?.submit();
  }, [dbList]);

  // 表格
  const columns: ProColumns<DSP_API.LoginAuditData>[] = [
    {
      title: '数据库',
      dataIndex: 'dbId',
      valueType: 'select',
      hideInTable: true,
      formItemProps: {
        rules: [requiredRule],
      },
      fieldProps: {
        loading: dbListLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'dbName',
        },
        options: dbList,
      },
    },
    {
      title: '系统用户名',
      dataIndex: 'osUserName',
      width: 150,
      search: false,
      ellipsis: true,
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '用户主机',
      dataIndex: 'userHost',
      search: false,
      width: 150,
      ellipsis: true,
    },
    {
      title: '终端',
      dataIndex: 'terminal',
      search: false,
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作对象',
      dataIndex: 'objName',
      search: false,
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作类型',
      dataIndex: 'actionName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '登录时间',
      width: 180,
      dataIndex: 'loginTime',
      renderText: formatDate,
      hideInSearch: true,
      ellipsis: true,
    },
    {
      title: '登录时间',
      width: 180,
      dataIndex: 'loginTime',
      renderText: formatDate,
      valueType: 'dateTimeRange',
      search: {
        transform: (value: any) => ({ startTime: value[0], endTime: value[1] }),
      },
      hideInTable: true,
    },
    {
      title: '操作内容',
      search: false,
      children: [
        // {
        //   title: '对象',
        //   dataIndex: 'objName',
        // },
        {
          title: '语句',
          dataIndex: 'sqlText',
        },
        {
          title: '值',
          dataIndex: 'sqlBind',
        },
      ],
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<DSP_API.LoginAuditData>
        {...defaultTableConfig}
        form={{
          ignoreRules: false,
        }}
        formRef={formRef}
        search={{
          collapsed: false,
          collapseRender: () => false,
        }}
        actionRef={tableRef}
        columns={columns}
        headerTitle="日志列表"
        request={async (params) => {
          const { dbId, userName, startTime, endTime, actionName, current, pageSize } =
            Object.fromEntries(
              Object.entries(params).filter(([, value]) => value !== undefined && value !== ''),
            );
          const search = {
            dbId: dbId,
            ACTION_NAME: actionName,
            USERNAME: userName,
            LOGIN_TIME: { startTime: startTime, endTime: endTime },
          };
          if (dbId) {
            const msg = await getAllUsingPOST1({
              ...{ pageNum: current, pageSize: pageSize },
              search,
            });
            const success = msg.code === 100;
            if (success) {
              return {
                data: msg?.data?.records || [],
                success: true,
                total: msg?.data?.total,
              };
            }
          }
          return { success: true };
        }}
        polling={5000}
      />
    </PageContainer>
  );
};

export default AccessAuditJournal;
