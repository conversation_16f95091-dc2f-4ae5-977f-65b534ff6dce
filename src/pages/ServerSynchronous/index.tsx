import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import BaseListContext from '@/components/Context/BaseListContext';
import { useDBList } from '@/components/hooks/useDBList';
import { useServerCollectionList } from '@/components/hooks/useServerCollectionList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { MASK_CONFIG_STATUS } from '@/enums';
import {
  batchDelUsingPOST,
  executeDataSyncUsingGET,
  getAllUsingGET3,
  stopSyncConfigIdUsingPOST,
} from '@/services/dsp/dataSyncConfigController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import { DefaultOptionType } from 'antd/lib/select';
import ServerSynchronousDrawerForm from './components/ServerSynchronousDrawerForm';

const ServerSynchronous: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.DataSyncConfig[]>([]);
  const [initialValues, setInitialValues] = useState<API.DataSyncConfig | undefined>();
  const [drawerVisit, setDrawerVisit] = useState(false);
  const { dbList, loading: dbLoading } = useDBList();
  const { serverCollectionList, loading: serverCollectionLoading } = useServerCollectionList();

  const onEdit = (record: API.DataSyncConfig) => {
    setInitialValues(record);
    setDrawerVisit(true);
  };

  const { run: deleteRecord } = useRequest((ids) => batchDelUsingPOST(ids), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 100) return;
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleDelete = async (rows: API.DataSyncConfig[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除同步“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  const { run: executeRecord } = useRequest((value) => executeDataSyncUsingGET(value), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 100) return;
      message.success(res.message);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleExecute = async (record: API.DataSyncConfig) => {
    Modal.confirm({
      title: `确认执行`,
      content: `您确定要执行同步“${record?.name}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        executeRecord({ dataSyncConfigId: record?.id });
      },
    });
  };
  const { run: stopRecord } = useRequest((row) => stopSyncConfigIdUsingPOST(row), {
    manual: true,
    onSuccess: (res) => {
      if (res.code !== 100) return;
      message.success(`停止成功`);
      tableRef.current?.reloadAndRest?.();
    },
    formatResult: (res) => res,
  });
  const handleStop = async (record: API.DataSyncConfig) => {
    Modal.confirm({
      title: `确认执行`,
      content: `您确定要停止同步“${record?.name}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        stopRecord(record);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.DataSyncConfig>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 100,
      ellipsis: true,
    },
    {
      title: '服务器数据采集名称',
      dataIndex: 'dataCollectionId',
      width: 120,
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        loading: serverCollectionLoading,
        fieldNames: {
          label: 'name',
          value: 'id',
        },
        options: serverCollectionList as DefaultOptionType[],
      },
    },
    {
      title: '目标数据库名称',
      dataIndex: 'targetDbId',
      width: 120,
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        loading: dbLoading,
        options: dbList as DefaultOptionType[],
        fieldNames: {
          label: 'dbName',
          value: 'id',
        },
      },
    },
    {
      title: '开始执行时间',
      dataIndex: 'startTime',
      valueType: 'dateTime',
      width: 120,
      ellipsis: true,
    },
    {
      title: '最终执行时间',
      dataIndex: 'lastTime',
      valueType: 'dateTime',
      width: 120,
      ellipsis: true,
    },
    {
      title: '最终状态',
      dataIndex: 'lastStatus',
      valueEnum: option2enum(MASK_CONFIG_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '当前状态',
      dataIndex: 'status',
      valueEnum: option2enum(MASK_CONFIG_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        const { status } = record;
        return (
          <Space>
            <Button
              type="link"
              className="ant-btn-link-table"
              key="detail"
              onClick={() => {
                history.push(
                  `/data-consume/server-synchronous/sync-details/${record.id}?tableNames=${record.tableNames}&topicName=${record.topicName}`,
                );
              }}
            >
              详情
            </Button>
            {status !== 0 && (
              <>
                <Button
                  type="link"
                  className="ant-btn-link-table"
                  key="execute"
                  onClick={() => handleExecute(record)}
                >
                  执行
                </Button>
                <Button
                  type="link"
                  className="ant-btn-link-table"
                  key="edit"
                  onClick={() => onEdit(record)}
                >
                  编辑
                </Button>
                <Button
                  type="link"
                  className="ant-btn-link-table"
                  key="del"
                  onClick={() => handleDelete([record])}
                >
                  删除
                </Button>
              </>
            )}
            {status !== 3 && (
              <Button
                type="link"
                className="ant-btn-link-table"
                key="stop"
                onClick={() => handleStop(record)}
              >
                停止
              </Button>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.DataSyncConfig>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: record?.status !== 3,
          }),
        }}
        columns={columns}
        headerTitle="服务器同步列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setDrawerVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建同步
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUsingGET1Params>({ ...params }, getAllUsingGET3)
        }
        // polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <BaseListContext.Provider
        value={{ dbList, dbLoading, serverCollectionList, serverCollectionLoading }}
      >
        <ServerSynchronousDrawerForm
          initialValues={initialValues}
          open={drawerVisit}
          onOpenChange={(visible) => {
            setDrawerVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default ServerSynchronous;
