import BaseListContext from '@/components/Context/BaseListContext';
import { addUsingPOST2, modifyConfigUsingPUT } from '@/services/dsp/dataSyncConfigController';
import { requiredRule } from '@/utils/setting';
import { TableNamesReg, topicNamesReg } from '@/utils/validator';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDateTimePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useContext, useRef } from 'react';
// const disabledDate: RangePickerProps['disabledDate'] = (current) => {
//   return current && current < dayjs().subtract(1, 'day').endOf('day');
// };

const ServerSynchronousDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.id;
  const formRef = useRef<ProFormInstance>();
  const { dbList, dbLoading, serverCollectionList, serverCollectionLoading } =
    useContext(BaseListContext);

  return (
    <DrawerForm<API.DataSyncConfig>
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = isEdit
          ? await modifyConfigUsingPUT(value)
          : await addUsingPOST2({ ...value, status: 3 });
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
        <ProFormText name="createTime" />
        <ProFormText name="lastStatus" />
        <ProFormText name="lastTime" />
        <ProFormText name="status" />
        <ProFormText name="updateTime" />
      </div>
      <ProFormText
        name="name"
        label="名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
        disabled={isEdit}
      />
      <ProFormSelect
        name="dataCollectionId"
        label="服务器数据采集配置"
        rules={[requiredRule]}
        fieldProps={{
          loading: serverCollectionLoading,
          showSearch: true,
          fieldNames: {
            value: 'id',
            label: 'name',
          },
          filterOption: true,
          optionFilterProp: 'label',
        }}
        options={serverCollectionList as DefaultOptionType[]}
        disabled={isEdit}
      />
      <ProFormTextArea
        name="topicName"
        label="主题名称"
        placeholder="请输入"
        tooltip="请输入主题名称,多个名称请用,分隔！"
        rules={[
          {
            pattern: topicNamesReg,
            message: '多个主题名称必须用,分割',
          },
        ]}
        fieldProps={{
          autoSize: {
            minRows: 1,
            maxRows: 2,
          },
        }}
      />
      <ProFormSelect
        name="targetDbId"
        label="目标数据库"
        rules={[requiredRule]}
        fieldProps={{
          loading: dbLoading,
          showSearch: true,
          fieldNames: {
            value: 'id',
            label: 'dbName',
          },
        }}
        options={dbList as DefaultOptionType[]}
      />
      <ProFormDateTimePicker name="startTime" label="同步开始时间" width="lg" />
      <ProFormTextArea
        name="tableNames"
        label="数据库表配置"
        placeholder="请输入"
        tooltip="请输入数据库表配置，格式为库名.表名，多个配置请用,分隔！"
        rules={[
          {
            pattern: TableNamesReg,
            message: '格式必须为数据库名.表名，多个配置请用,分割',
          },
        ]}
        fieldProps={{
          autoSize: {
            minRows: 1,
            maxRows: 2,
          },
        }}
      />
    </DrawerForm>
  );
};

export default ServerSynchronousDrawerForm;
