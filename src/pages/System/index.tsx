import { linkUserUsingGET } from '@/services/dsp/alarmController';
import { getServersUsingGET } from '@/services/dsp/serverConfigController';
import {
  getAllUsingGET9,
  getSystemConfigsEnumUsingGET,
} from '@/services/dsp/systemConfigsController';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Layout, Menu } from 'antd';
import { useState } from 'react';
import AlarmForm from './components/AlarmForm';
import InterfaceForm from './components/InterfaceForm';
import MailBoxForm from './components/MailBoxForm';
import ServerForm from './components/ServerForm';
import SmsForm from './components/SmsForm';

const items = [
  { key: 'mailbox', label: '邮箱管理' },
  { key: 'alarm', label: '告警策略' },
  { key: 'server', label: '服务器设置' },
  { key: 'interface', label: '接口设置' }, //note 目前只考虑单个对象，后期可能存在多个对象
  { key: 'sms', label: '短信服务配置' },
];
const System: React.FC = () => {
  const [menuKey, setMenuKey] = useState('mailbox');
  const { data: enumData = {} } = useRequest(() => getSystemConfigsEnumUsingGET());
  const { data: serverData = [], run: getServerData } = useRequest(() => getServersUsingGET());
  const { data: alarmData = [], run: getAlarmData } = useRequest(() => linkUserUsingGET());
  const { data = {}, run: getInterfaceData } = useRequest(() =>
    getAllUsingGET9({
      pageNum: 1,
      pageSize: 100000,
    }),
  );
  const initialValues = data?.records;
  const InterfaceData =
    initialValues?.length > 0
      ? [{ ...initialValues?.at(0) }, { ...initialValues?.at(1) }]
      : [
          {
            systemKey: Object.values(enumData).at(0),
            systemName: Object.keys(enumData).at(0),
          },
          {
            systemKey: Object.values(enumData).at(1),
            systemName: Object.keys(enumData).at(1),
          },
        ];

  return (
    <PageContainer header={{ title: false }} className="detail-container">
      <Layout>
        <Layout.Sider width={256}>
          <Menu
            mode="inline"
            onClick={({ key }) => setMenuKey(key)}
            items={items}
            defaultSelectedKeys={['mailbox']}
            style={{ height: '100%' }}
          />
        </Layout.Sider>
        <Layout.Content style={{ backgroundColor: '#fff' }}>
          {menuKey === 'mailbox' && <MailBoxForm />}
          {menuKey === 'alarm' && (
            <AlarmForm
              initialValues={alarmData?.at(0)}
              onFinish={async () => {
                getAlarmData();
              }}
            />
          )}
          {menuKey === 'server' && (
            <ServerForm
              initialValues={serverData?.at(0)}
              onFinish={async () => {
                getServerData();
              }}
            />
          )}
          {menuKey === 'interface' && (
            <InterfaceForm
              initialValues={InterfaceData}
              onFinish={async () => {
                getInterfaceData();
              }}
            />
          )}
          {menuKey === 'sms' && <SmsForm />}
        </Layout.Content>
      </Layout>
    </PageContainer>
  );
};

export default System;
