import { addServersUsingPOST, testLinkUsingGET } from '@/services/dsp/serverConfigController';
import { ProForm, ProFormInstance, ProFormProps, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import Button from 'antd/es/button';
import { useRef, useState } from 'react';

const ServerForm: React.FC<ProFormProps> = ({ initialValues, onFinish }) => {
  const formRef = useRef<ProFormInstance>();
  const [linkDisabled, setLinkDisabled] = useState(true);
  const isEdit = initialValues?.id;

  //服务器测试连接
  const { run: testServerLink } = useRequest((link) => testLinkUsingGET({ link }), {
    manual: true,
    onSuccess: () => {
      message.success('服务器测试连接成功');
      setLinkDisabled(false);
    },
  });
  const onTestLink = async () => {
    const formData = (await formRef.current?.validateFieldsReturnFormatValue?.()) || {};
    const link = formData.cntServers;
    testServerLink(link);
  };

  return (
    <ProForm
      formRef={formRef}
      initialValues={isEdit ? initialValues : undefined}
      submitter={{
        searchConfig: {
          resetText: '取消',
          submitText: '保存',
        },
        resetButtonProps: {
          style: { display: 'none' },
        },
        submitButtonProps: {
          disabled: linkDisabled,
        },
        // 完全自定义整个区域
        render: (props, doms) => {
          return [
            <Button htmlType="button" key="testLink" onClick={onTestLink}>
              测试
            </Button>,
            ...doms,
          ];
        },
      }}
      style={{ height: '100%', padding: '40px' }}
      onFinish={async (value) => {
        const msg = await addServersUsingPOST(value);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        setLinkDisabled(true);
        return success;
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProFormText width="lg" name="servers" label="队列服务器" placeholder="请输入" />
      <ProFormText width="lg" name="cntServers" label="CONNECT服务器" placeholder="请输入" />
    </ProForm>
  );
};

export default ServerForm;
