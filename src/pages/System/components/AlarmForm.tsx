import { addAlarmUsingPOST, findUserNameUsingGET } from '@/services/dsp/alarmController';
import {
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormProps,
  ProFormSelect,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { useRef } from 'react';

const AlarmForm: React.FC<ProFormProps> = ({ initialValues, onFinish }) => {
  const formRef = useRef<ProFormInstance>();

  return (
    <ProForm
      formRef={formRef}
      initialValues={initialValues}
      submitter={{
        searchConfig: {
          resetText: '取消',
          submitText: '保存',
        },
        resetButtonProps: {
          style: { display: 'none' },
        },
        submitButtonProps: {},
        // 完全自定义整个区域
        render: (props, doms) => {
          return [...doms];
        },
      }}
      style={{ height: '100%', padding: '40px' }}
      onFinish={async (value) => {
        const msg = await addAlarmUsingPOST(value);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
    >
      <ProFormSelect
        width="lg"
        name="userName"
        label="联系人"
        placeholder="请选择"
        request={async () => {
          const msg = await findUserNameUsingGET();
          const options = (msg.data as string[]).map((item) => {
            return { key: item, label: item };
          });
          return options;
        }}
      />
      <ProFormDigit
        label="系统容量小于"
        name="sysRemain"
        width="lg"
        min={10}
        max={100}
        fieldProps={{
          defaultValue: 10,
          step: 10,
          precision: 0,
          addonAfter: '%',
        }}
      />
    </ProForm>
  );
};

export default AlarmForm;
