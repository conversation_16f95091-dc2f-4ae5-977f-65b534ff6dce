import { addEmailsUsingPOST, testemailsUsingPOST } from '@/services/dsp/emailsController';
import { PortReg } from '@/utils/validator';
import { ProForm, ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import Button from 'antd/es/button';
import { useRef, useState } from 'react';

const MailBoxForm: React.FC = () => {
  const formRef = useRef<ProFormInstance>();
  const [linkDisabled, setLinkDisabled] = useState(true);

  //测试邮箱
  const { run: testEmailLink } = useRequest((email) => testemailsUsingPOST(email), {
    manual: true,
    onSuccess: () => {
      message.success('邮箱设置测试成功');
      setLinkDisabled(false);
    },
  });
  const onTestLink = async () => {
    const formData = (await formRef.current?.validateFieldsReturnFormatValue?.()) || {};
    testEmailLink(formData);
  };

  return (
    <ProForm
      formRef={formRef}
      submitter={{
        searchConfig: {
          resetText: '取消',
          submitText: '保存',
        },
        resetButtonProps: {
          style: { display: 'none' },
        },
        submitButtonProps: {
          disabled: linkDisabled,
        },
        // 完全自定义整个区域
        render: (props, doms) => {
          return [
            <Button htmlType="button" key="testLink" onClick={onTestLink}>
              测试
            </Button>,
            ...doms,
          ];
        },
      }}
      style={{ height: '100%', padding: '40px' }}
      onFinish={async (value) => {
        const msg = await addEmailsUsingPOST(value);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
        }
        setLinkDisabled(true);
        return success;
      }}
    >
      <ProFormText width="lg" name="smtpServer" label="SMTP服务器" placeholder="请输入" />
      <ProFormText
        width="lg"
        name="serverPort"
        label="端口号"
        placeholder="请输入"
        rules={[
          {
            pattern: PortReg,
            message: '请输入正确的端口号',
          },
        ]}
      />
      <ProFormSelect
        width="lg"
        name="security"
        label="安全"
        placeholder="请选择"
        valueEnum={{
          TLS: 'TLS',
          SSL: 'SSL',
        }}
      />
      <ProFormText width="lg" name="smtpUser" label="SMTP用户名" placeholder="请输入" />
      <ProFormText.Password width="lg" name="smtpPassword" label="SMTP密码" placeholder="请输入" />
    </ProForm>
  );
};

export default MailBoxForm;
