import {
  getSmsConfigUsingGET,
  modifySmsConfigUsingPUT,
} from '@/services/dsp/systemConfigsController';
import { ProForm, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import { useRef } from 'react';

const SmsForm: React.FC = () => {
  const formRef = useRef<ProFormInstance>();

  // 获取SMS配置数据
  const { run: getSmsData } = useRequest(() => getSmsConfigUsingGET(), {
    onSuccess: (data) => {
      if (data?.data) {
        formRef.current?.setFieldsValue(data.data);
      }
    },
    formatResult: (res) => res,
  });

  return (
    <ProForm
      formRef={formRef}
      submitter={{
        searchConfig: {
          resetText: '取消',
          submitText: '保存',
        },
        resetButtonProps: {
          style: { display: 'none' },
        },
        submitButtonProps: {},
        // 完全自定义整个区域
        render: (props, doms) => {
          return [...doms];
        },
      }}
      style={{ height: '100%', padding: '40px' }}
      onFinish={async (value) => {
        const msg = await modifySmsConfigUsingPUT(value);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          getSmsData();
        }
        return success;
      }}
    >
      <ProFormText
        width="lg"
        name="sms-url"
        label="短信服务地址"
        placeholder="请输入短信服务地址"
      />
      <ProFormText
        width="lg"
        name="sms-token"
        label="短信服务Token"
        placeholder="请输入短信服务Token"
      />
      <ProFormText.Password
        width="lg"
        name="sms-secret"
        label="短信服务密钥"
        placeholder="请输入短信服务密钥"
      />
    </ProForm>
  );
};

export default SmsForm;
