import { addUsingPOST4, modifyConfigUsingPUT2 } from '@/services/dsp/systemConfigsController';
import {
  ProForm,
  ProFormInstance,
  ProFormProps,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { useRef } from 'react';

const InterfaceForm: React.FC<ProFormProps> = ({ initialValues, onFinish }) => {
  console.warn('🚀 ~ initialValues:', initialValues);

  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.at(0)?.id;

  return (
    <ProForm
      formRef={formRef}
      submitter={{
        searchConfig: {
          resetText: '取消',
          submitText: '保存',
        },
        resetButtonProps: {
          style: { display: 'none' },
        },
        submitButtonProps: {},
        // 完全自定义整个区域
        render: (props, doms) => {
          return [...doms];
        },
      }}
      style={{ height: '100%', padding: '40px' }}
      onFinish={async (value) => {
        const form = [
          {
            systemKey: 'platformHost',
            systemName: '全量数据平台表结构信息查询接口',
            systemValue: value.platformHost,
            id: initialValues?.at(0)?.id || undefined,
          },
          {
            systemKey: 'platformToken',
            systemName: '全量数据平台表结构信息查询接口TOKEN',
            systemValue: value.platformToken,
            id: initialValues?.at(1)?.id || undefined,
          },
        ];
        const msg = isEdit ? await modifyConfigUsingPUT2(form) : await addUsingPOST4(form);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
    >
      <ProFormText
        width="lg"
        name="platformHost"
        initialValue={initialValues?.at(0)?.systemValue || ''}
        label="全量数据平台表结构信息查询接口"
        placeholder="请输入"
      />
      <ProFormTextArea
        width="lg"
        name="platformToken"
        initialValue={initialValues?.at(1)?.systemValue || ''}
        fieldProps={{ autoSize: { minRows: 4, maxRows: 6 } }}
        label="全量数据平台表结构信息查询接口TOKEN"
        placeholder="请输入"
      />
    </ProForm>
  );
};

export default InterfaceForm;
