import AutoLogin from '@/components/AutoLogin';
import Footer from '@/components/Footer';
import { loginUsingPOST } from '@/services/dsp/userController';
import { getCredentialsCookie } from '@/utils';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { LoginForm, ProFormText } from '@ant-design/pro-components';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { Helmet, history, useModel } from '@umijs/max';
import { message } from 'antd';
import classNames from 'classnames/bind';
import React from 'react';
import { flushSync } from 'react-dom';
import Settings from '../../../../config/defaultSettings';
import styles from './index.less';

const cx = classNames.bind(styles);

const Login: React.FC = () => {
  const { setInitialState } = useModel('@@initialState');

  const containerClassName = useEmotionCss(() => {
    return {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage: "url('/images/loginBg.jpeg')",
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
    };
  });

  const credentialsCookie = getCredentialsCookie();
  const { account, password } = credentialsCookie || {};
  if (account && password) return <AutoLogin account={account} password={password} />;

  const handleSubmit = async (values: API.User) => {
    try {
      // 登录
      const res = await loginUsingPOST(values);
      const { code, data = {} } = res || {};
      const { userInfo } = data;
      if (code === 100) {
        message.success('登录成功');
        localStorage.setItem('RKLINK_DSP_TOKEN', data?.token || '');
        const urlParams = new URL(window.location.href).searchParams;
        flushSync(() => {
          setInitialState((s = {}) => ({
            ...s,
            currentUser: userInfo,
          }));
        });

        setTimeout(() => {
          history.push(urlParams.get('redirect') || '/');
          window.location.reload();
        }, 500);
        return;
      }
    } catch (error) {
      console.log('🚗 🚗 🚗 ~ file: index.tsx:47 ~ handleSubmit ~ error:', error);
    }
  };

  return (
    <div className={containerClassName}>
      <Helmet>
        <title>登录 - {Settings.title}</title>
      </Helmet>
      <div className={cx('login-content')}>
        <div className={cx('login-bg')} />
        <div className={cx('login-form')}>
          <LoginForm
            contentStyle={{
              minWidth: 280,
              maxWidth: '75vw',
            }}
            // logo={<img alt="logo" src="/images/logo_simple.png" />}
            title="DSP数据融合共享平台"
            initialValues={{
              autoLogin: true,
            }}
            onFinish={async (values) => {
              await handleSubmit(values);
            }}
          >
            <ProFormText
              name="userName"
              fieldProps={{
                size: 'large',
                prefix: <UserOutlined />,
              }}
              placeholder="请输入用户名"
              rules={[
                {
                  required: true,
                  message: '请输入用户名!',
                },
              ]}
            />
            <ProFormText.Password
              name="password"
              fieldProps={{
                size: 'large',
                prefix: <LockOutlined />,
              }}
              placeholder="请输入密码"
              rules={[
                {
                  required: true,
                  message: '请输入密码！',
                },
              ]}
            />
          </LoginForm>
          <Footer />
        </div>
      </div>
    </div>
  );
};

export default Login;
