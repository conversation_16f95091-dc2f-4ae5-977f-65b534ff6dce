import {
  getSyncConfigUsingGET,
  updateSyncConfigUsingPUT,
} from '@/services/dsp/dataSyncConfigInfoController';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Card, Input, message, Space } from 'antd';
import React, { useState } from 'react';

const DatabasePrimaryKeyConfig: React.FC = () => {
  const [jsonContent, setJsonContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');

  // 获取配置数据
  const { loading, run: fetchConfig } = useRequest(() => getSyncConfigUsingGET(), {
    onSuccess: (response) => {
      if (response.data) {
        const content =
          typeof response.data === 'string'
            ? response.data
            : JSON.stringify(response.data, null, 2);
        setJsonContent(content);
        setOriginalContent(content);
      } else {
        message.error('获取配置失败');
      }
    },
    formatResult: (res) => res,
  });

  // 验证JSON格式
  const validateJson = (jsonString: string): boolean => {
    try {
      JSON.parse(jsonString);
      return true;
    } catch (error) {
      return false;
    }
  };

  // 保存配置
  const { run: handleSave, loading: saving } = useRequest(
    (params) => updateSyncConfigUsingPUT(params),
    {
      manual: true,
      onSuccess: (msg) => {
        const success = msg.code === 100;
        if (success) {
          message.success('保存成功');
          setOriginalContent(jsonContent);
        }
      },
      formatResult: (res) => res,
    },
  );

  // 保存配置处理函数
  const onSave = () => {
    if (!jsonContent.trim()) {
      message.error('配置内容不能为空');
      return;
    }

    if (!validateJson(jsonContent)) {
      message.error('JSON格式不正确，请检查后重试');
      return;
    }

    handleSave({
      json: jsonContent,
    });
  };

  // 重置配置
  const handleReset = () => {
    setJsonContent(originalContent);
    message.info('已重置为原始配置');
  };

  // 格式化JSON
  const handleFormat = () => {
    try {
      const parsed = JSON.parse(jsonContent);
      const formatted = JSON.stringify(parsed, null, 2);
      setJsonContent(formatted);
      message.success('格式化成功');
    } catch (error) {
      message.error('JSON格式不正确，无法格式化');
    }
  };

  const hasChanges = jsonContent !== originalContent;

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <Card loading={loading}>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button type="primary" onClick={onSave} loading={saving} disabled={!hasChanges}>
              保存配置
            </Button>
            <Button onClick={handleReset} disabled={!hasChanges}>
              重置
            </Button>
            <Button onClick={handleFormat}>格式化JSON</Button>
            <Button onClick={fetchConfig} loading={loading}>
              刷新
            </Button>
          </Space>
        </div>

        <Input.TextArea
          value={jsonContent}
          onChange={(e) => setJsonContent(e.target.value)}
          placeholder="请输入JSON配置内容..."
          rows={25}
          style={{
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            fontSize: '14px',
            lineHeight: '1.5',
          }}
        />

        {hasChanges && (
          <div style={{ marginTop: 8, color: '#faad14' }}>* 配置已修改，请记得保存</div>
        )}
      </Card>
    </PageContainer>
  );
};

export default DatabasePrimaryKeyConfig;
