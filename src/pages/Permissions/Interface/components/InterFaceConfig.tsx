import { INTERFACE_METHODS_COLOR } from '@/enums';
import {
  scanPermissionsUsingGET,
  updatePermissionsUsingPOST,
} from '@/services/dsp/permissionsController';
import { getRandomId, onSuccessAndGoBack } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import {
  FooterToolbar,
  PageContainer,
  ProCard,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Space, Tag } from 'antd';
import { useRef, useState } from 'react';

const formatData = (arr: string[]) => {
  return arr?.map((item) => {
    const list = item.split(',');
    return {
      id: getRandomId(),
      url: list?.[0],
      methods: list?.[1]?.replace(/\[|]/g, ''),
    };
  });
};
const InterFaceConfig = () => {
  const formRef = useRef<ProFormInstance>();
  const [extraDataSource, setExtraDataSource] = useState<Record<string, any>[]>([]);
  const [missDataSource, setMissDataSource] = useState<Record<string, any>[]>([]);
  const [selectedExtraRows, setSelectedExtraRows] = useState<Record<string, any>[]>([]);
  const [selectedMissRows, setSelectedMissRows] = useState<Record<string, any>[]>([]);

  const { run, loading } = useRequest((value) => updatePermissionsUsingPOST(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });

  const columns: ProColumns<API.FuncList>[] = [
    {
      title: '接口地址',
      dataIndex: 'url',
      fixed: 'left',
      width: 150,
      ellipsis: true,
    },
    {
      title: '请求方法',
      dataIndex: 'methods',
      width: 100,
      ellipsis: true,
      renderText: (text) => <Tag color={INTERFACE_METHODS_COLOR?.[text]}>{text}</Tag>,
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
      className="detail-container"
    >
      <ProForm
        formRef={formRef}
        submitter={{
          searchConfig: {
            resetText: '取消',
            submitText: '保存',
          },
          onReset: () => {
            history.go(-1);
          },
          //自定义整个区域
          render: (props, doms) => {
            return (
              <FooterToolbar
                extra={
                  <Space>
                    已选择:
                    <Space>
                      新增接口
                      <a
                        style={{
                          fontWeight: 600,
                        }}
                      >
                        {selectedExtraRows.length}
                      </a>
                      项
                    </Space>
                    <Space>
                      过期接口
                      <a
                        style={{
                          fontWeight: 600,
                        }}
                      >
                        {selectedMissRows.length}
                      </a>
                      项
                    </Space>
                  </Space>
                }
              >
                {doms}
              </FooterToolbar>
            );
          },
          submitButtonProps: {
            loading,
          },
          resetButtonProps: {
            style: {
              marginLeft: 8,
            },
          },
        }}
        onFinish={async () => {
          const formData = {
            extraDifferences: selectedExtraRows.map((item) => `${item.url},[${item.methods}]`),
            missDifferences: selectedMissRows.map((item) => `${item.url},[${item.methods}]`),
          };
          await run(formData);
        }}
        request={async () => {
          const msg = await scanPermissionsUsingGET();
          const { extraDifferences, missDifferences } = msg?.data || {};
          setExtraDataSource(formatData(extraDifferences));
          setMissDataSource(formatData(missDifferences));
          if (msg.code !== 100) return {};
          return msg.data;
        }}
      >
        <div style={{ display: 'none' }}>
          <ProFormText width="md" name="extraDifferences" />
          <ProFormText width="md" name="missDifferences" />
        </div>
        <ProCard split="vertical" title="接口列表">
          <ProCard colSpan="50%">
            <ProTable
              ghost={true}
              {...defaultTableConfig}
              options={false}
              pagination={false}
              search={false}
              columns={columns}
              headerTitle="新增接口"
              rowSelection={{
                onChange: (selectedKeys, selectedRows) => setSelectedExtraRows(selectedRows),
              }}
              dataSource={extraDataSource}
              scroll={{ x: '100%' }}
            />
          </ProCard>
          <ProCard>
            <ProTable
              ghost={true}
              {...defaultTableConfig}
              options={false}
              pagination={false}
              search={false}
              columns={columns}
              headerTitle="过期接口"
              rowSelection={{
                onChange: (selectedKeys, selectedRows) => setSelectedMissRows(selectedRows),
              }}
              dataSource={missDataSource}
              scroll={{ x: '100%' }}
            />
          </ProCard>
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default InterFaceConfig;
