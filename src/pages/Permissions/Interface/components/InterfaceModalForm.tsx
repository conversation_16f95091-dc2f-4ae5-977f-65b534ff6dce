import { useFeatureMapList } from '@/components/hooks/useFeatureMapList';
import { modifyPermissionsUsingPOST } from '@/services/dsp/permissionsController';
import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormInstance,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { useRef } from 'react';

const InterfaceModalForm: React.FC<ModalFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const { featureList, loading } = useFeatureMapList();

  return (
    <ModalForm
      width={736}
      formRef={formRef}
      title={'配置功能权限'}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const msg = await modifyPermissionsUsingPOST(value);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      initialValues={initialValues}
    >
      <div style={{ display: 'none' }}>
        <ProFormText width="md" name="id" />
      </div>
      <ProForm.Group>
        <ProFormTreeSelect
          width="md"
          name="funcListId"
          label="功能名称"
          placeholder="请输入"
          rules={[requiredRule]}
          fieldProps={{
            showSearch: true,
            treeData: featureList,
            loading,
            fieldNames: {
              label: 'title',
              value: 'key',
              children: 'children',
            },
            treeNodeFilterProp: 'title',
            filterTreeNode: true,
          }}
        />
        <ProFormText
          width="md"
          disabled
          name="url"
          label="接口地址"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default InterfaceModalForm;
