import { useFeatureMapList } from '@/components/hooks/useFeatureMapList';
import { INTERFACE_METHODS_COLOR, USER_STATUS } from '@/enums';
import { getAllPermissionsUsingGET } from '@/services/dsp/permissionsController';
import { option2enum, queryPagingTable, transformTreeToArray } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history } from '@umijs/max';

import { Button, Space, Tag } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import { useRef, useState } from 'react';
import InterfaceModalForm from './components/InterfaceModalForm';

/**
 * 功能权限 设置每个模块的增删改查等功能 一对一关系
 */

const Interface = () => {
  const tableRef = useRef<ActionType>();
  // const [selectedRows, setSelectedRows] = useState<API.Permissions[]>([]);
  const [modalVisit, setModalVisit] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<API.Permissions>();
  const { featureList, loading } = useFeatureMapList();

  // const { run: batchDeleteByIds } = useRequest((ids) => batchDelPermissionsUsingDELETE(ids), {
  //   manual: true,
  //   onSuccess: () => {
  //     message.success('删除成功');
  //     tableRef.current?.reloadAndRest?.();
  //   },
  // });
  // const handleDelete = (rows: API.Permissions[]) => {
  //   const ids: string[] = [];
  //   const names: string[] = [];
  //   rows.forEach((item) => {
  //     ids.push(String(item.id)!);
  //     const name = transformTreeToArray(featureList)?.find(
  //       (it) => it.id === item.funcListId,
  //     )?.newName;
  //     names.push(name!);
  //   });
  //   Modal.confirm({
  //     title: '确认删除',
  //     content: `您确定要删除“${names.join('、')}”吗?`,
  //     okText: '确认',
  //     cancelText: '取消',
  //     onOk: async () => {
  //       batchDeleteByIds(ids);
  //     },
  //   });
  // };

  const columns: ProColumns<API.Permissions>[] = [
    {
      title: '功能名称',
      dataIndex: 'funcListId',
      fixed: 'left',
      width: 120,
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        loading,
        options: transformTreeToArray(featureList) as API.Permissions as DefaultOptionType[],
        fieldNames: {
          value: 'id',
          label: 'newName',
        },
      },
    },
    {
      title: '接口地址',
      dataIndex: 'url',
      width: 200,
      ellipsis: true,
    },
    {
      title: '请求方法',
      dataIndex: 'requestMethod',
      width: 100,
      ellipsis: true,
      renderText: (text) => <Tag color={INTERFACE_METHODS_COLOR?.[text]}>{text}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      ellipsis: true,
      valueEnum: option2enum(USER_STATUS),
    },

    {
      title: '操作',
      key: 'options',
      width: 150,
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space>
            <Button
              key="edit"
              type="link"
              onClick={() => {
                setModalVisit(true);
                setInitialValues(record);
              }}
            >
              配置
            </Button>
            {/* <Button key="del" type="link" onClick={() => handleDelete([record])}>
              删除
            </Button> */}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        actionRef={tableRef}
        search={false}
        columns={columns}
        headerTitle="接口权限列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              onClick={() => {
                history.push('/permissions/interface/config');
              }}
            >
              更新接口
            </Button>,
          ],
        }}
        // rowSelection={{
        //   onChange: (selectedKeys, selectedRows) => setSelectedRows(selectedRows),
        // }}
        request={async (params) =>
          queryPagingTable<API.getAllPermissionsUsingGETParams>(
            { ...params },
            getAllPermissionsUsingGET,
          )
        }
        scroll={{ x: '100%' }}
      />
      {/* <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} /> */}
      <InterfaceModalForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Interface;
