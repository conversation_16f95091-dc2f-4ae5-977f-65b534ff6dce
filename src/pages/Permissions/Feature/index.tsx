import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import {
  batchDelFuncListUsingDELETE,
  getFuncListMapUsingGET,
} from '@/services/dsp/funcListController';
import { transformFeatureMapToTree, transformTreeToArray } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';

import { Button, message, Modal, Space } from 'antd';
import { useRef, useState } from 'react';
import FeatureModalForm from './components/FeatureModalForm';
import OperationModalForm from './components/OperationModalForm';

/**
 * 功能权限 设置每个模块的增删改查
 */
const Feature = () => {
  const tableRef = useRef<ActionType>();
  const [selectedRows, setSelectedRows] = useState<Record<string, any>[]>([]);

  const [modalVisit, setModalVisit] = useState<boolean>(false);
  const [modalOperationVisit, setModalOperationVisit] = useState<boolean>(false);
  const [initialOperationValues, setInitialOperationValues] = useState<API.FuncList>();
  const [initialValues, setInitialValues] = useState<API.FuncList>();

  const handleEdit = (record: API.FuncList) => {
    if (record.parentId) {
      setInitialOperationValues(record);
      setModalOperationVisit(true);
    } else {
      setModalVisit(true);
      setInitialValues(record);
    }
  };
  const { run: deleteRecord } = useRequest((ids) => batchDelFuncListUsingDELETE(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');

      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = (rows: Record<string, any>[]) => {
    const parentList = rows.filter((item) => item.children);
    const childList = rows.filter((item) => !item.children);
    const arr = [...childList, ...transformTreeToArray(parentList)];
    const records = Array.from(new Set(arr));

    const ids: string[] = [];
    const names: string[] = [];
    records.forEach((item: Record<string, any>) => {
      ids.push(String(item.id)!);
      names.push(item.newName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”功能吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  const columns: ProColumns<Record<string, any>>[] = [
    {
      title: '模块名称',
      dataIndex: 'moduleName',
      fixed: 'left',
      width: 120,
      ellipsis: true,
      renderText: (text, record) => {
        return !!record.moduleName && record.moduleName;
      },
    },
    {
      title: '模块地址',
      dataIndex: 'moduleNameEng',
      width: 150,
      ellipsis: true,
      renderText: (text, record) => {
        return !!record.moduleNameEng && record.moduleNameEng;
      },
    },
    {
      title: '功能名称',
      dataIndex: 'operationType',
      width: 120,
      ellipsis: true,
      renderText: (text, record) => {
        return !record.children && (record?.operationType ? `${record?.operationType}` : undefined);
      },
    },
    {
      title: '操作',
      key: 'options',
      width: 150,
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space>
            <Button key="edit" type="link" onClick={() => handleEdit(record)}>
              编辑
            </Button>
            <Button key="del" type="link" onClick={() => handleDelete([record])}>
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        actionRef={tableRef}
        search={false}
        columns={columns}
        headerTitle="功能权限列表"
        pagination={false}
        toolbar={{
          actions: [
            <Button
              key="addFeature"
              type="primary"
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建模块
            </Button>,
            <Button
              key="addOperation"
              type="primary"
              onClick={() => {
                setModalOperationVisit(true);
                setInitialOperationValues(undefined);
              }}
            >
              新建功能
            </Button>,
          ],
        }}
        rowSelection={{
          checkStrictly: false,
          onChange: (selectedKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        expandable={{
          columnTitle: 'operationType',
          childrenColumnName: 'children',
        }}
        request={async () => {
          const msg = await getFuncListMapUsingGET();
          const dataSource = transformFeatureMapToTree(msg?.data as Record<string, any>);
          return {
            data: dataSource || [],
            success: true,
          };
        }}
        scroll={{ x: '100%' }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      <FeatureModalForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
      <OperationModalForm
        initialValues={initialOperationValues}
        open={modalOperationVisit}
        onOpenChange={(visible) => {
          setModalOperationVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default Feature;
