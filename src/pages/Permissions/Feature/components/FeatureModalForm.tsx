import { addFuncListUsingPOST, modifyFuncListUsingPOST } from '@/services/dsp/funcListController';
import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { useRef } from 'react';

const FeatureModalForm: React.FC<ModalFormProps> = ({
  initialValues,
  open,
  onOpenChange,
  onFinish,
}) => {
  const formRef = useRef<ProFormInstance>();
  const isEdit = initialValues?.id;

  return (
    <ModalForm
      width={736}
      formRef={formRef}
      title={isEdit ? '修改模块' : '新建模块'}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { moduleName, moduleNameEng } = value;
        const msg = isEdit
          ? await modifyFuncListUsingPOST({ id: initialValues?.id, moduleName, moduleNameEng })
          : await addFuncListUsingPOST({ moduleName, moduleNameEng });
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      initialValues={initialValues}
    >
      <div style={{ display: 'none' }}>
        <ProFormText width="md" name="id" />
      </div>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="moduleName"
          label="模块名称"
          placeholder="请输入"
          rules={[requiredRule]}
        />
        <ProFormText
          width="md"
          name="moduleNameEng"
          label="模块地址"
          placeholder="请输入"
          rules={[requiredRule]}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default FeatureModalForm;
