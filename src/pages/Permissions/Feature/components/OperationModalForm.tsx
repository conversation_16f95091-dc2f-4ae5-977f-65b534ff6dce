import { useFeatureMapList } from '@/components/hooks/useFeatureMapList';
import { addFuncListUsingPOST, modifyFuncListUsingPOST } from '@/services/dsp/funcListController';
import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { useRef } from 'react';

const OperationModalForm: React.FC<ModalFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.id;
  const formRef = useRef<ProFormInstance>();
  const { featureList, loading } = useFeatureMapList();
  const featureOptions = featureList?.map((item: Record<string, any>) => ({
    label: item?.moduleName,
    value: parseInt(item?.id),
    moduleNameEng: item?.moduleNameEng,
  }));

  return (
    <ModalForm
      width={736}
      formRef={formRef}
      title={isEdit ? '编辑功能' : '新建功能'}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { operationType, parentId } = value;
        const msg = isEdit
          ? await modifyFuncListUsingPOST({ id: initialValues?.id, parentId, operationType })
          : await addFuncListUsingPOST({ parentId, operationType });
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      initialValues={{
        ...initialValues,
        moduleNameEng: featureOptions?.find((i) => i.value === initialValues?.parentId)
          ?.moduleNameEng,
      }}
    >
      <div style={{ display: 'none' }}>
        <ProFormDigit width="md" name="id" />
        <ProFormText width="md" name="moduleName" />
      </div>
      <ProForm.Group>
        <ProFormSelect
          width="md"
          name="parentId"
          label="模块名称"
          placeholder="请输入"
          rules={[requiredRule]}
          options={featureOptions}
          fieldProps={{
            loading,
            showSearch: true,
            onChange: (value, option) => {
              const { moduleNameEng } = option as Record<string, any>;
              formRef.current?.setFieldsValue({
                moduleNameEng: moduleNameEng,
              });
            },
            disabled: isEdit,
          }}
          transform={(value, namePath) => {
            return {
              [namePath]: parseInt(value),
              moduleName: featureOptions?.find((i) => i.value === value)?.label,
            };
          }}
        />
        <ProFormText
          disabled
          width="md"
          name="moduleNameEng"
          label="模块地址"
          placeholder="请输入"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="operationType"
          rules={[requiredRule]}
          label="功能名称"
          placeholder="请输入"
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default OperationModalForm;
