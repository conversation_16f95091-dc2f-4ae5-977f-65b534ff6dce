import { useFeatureMapList } from '@/components/hooks/useFeatureMapList';
import RKCol from '@/components/RKCol';
import withRouteEditing, { WithRouteEditingProps } from '@/hoc/withRouteEditing';
import {
  addRoleUsingPOST,
  getRoleByIdUsingGET,
  modifyRoleUsingPOST,
} from '@/services/dsp/roleController';
import { onSuccessAndGoBack, queryFormData } from '@/utils';
import { requiredRule } from '@/utils/setting';
import {
  FooterToolbar,
  Key,
  PageContainer,
  ProCard,
  ProForm,
  ProFormInstance,
  ProFormText,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Row, Space, Tree, TreeDataNode } from 'antd';
import { DataNode, TreeProps } from 'antd/es/tree';
import { useMemo, useRef, useState } from 'react';
// 递归函数，用于获取所有节点的 key
const getAllNodeKeys = (nodes: DataNode[]) => {
  return nodes.reduce((keys: DataNode['key'][], node: DataNode) => {
    keys.push(node.key);
    if (node.children) {
      keys.push(...getAllNodeKeys(node.children));
    }
    return keys;
  }, []);
};

export function findParentNodeKey(treeData: DataNode[], targetKeys: Key[]): Key[] {
  const ancestorKeys: Key[] = [];

  function searchAncestors(nodes: DataNode[], ancestors: Key[]) {
    nodes.forEach((node) => {
      if (targetKeys.includes(node.key)) {
        // Add all current ancestors when a target key is found
        ancestorKeys.push(...ancestors);
      }
      if (node?.children?.length) {
        // Extend the list of ancestors for the next level
        searchAncestors(node.children, [...ancestors, node.key]);
      }
    });
  }

  searchAncestors(treeData, []);
  return [...new Set(ancestorKeys)];
}

const InterFaceConfig: React.FC<WithRouteEditingProps> = ({ isEditPage, id }) => {
  const formRef = useRef<ProFormInstance>();
  const { featureList } = useFeatureMapList();
  // 新建
  const { run: add, loading: addLoading } = useRequest((value) => addRoleUsingPOST(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  // 修改
  const { run: edit, loading: editLoading } = useRequest((value) => modifyRoleUsingPOST(value), {
    manual: true,
    onSuccess: onSuccessAndGoBack,
    formatResult: (res) => res,
  });
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const allKeys: React.Key[] = useMemo(
    () => getAllNodeKeys(featureList as DataNode[]),
    [featureList],
  );

  const onExpand: TreeProps['onExpand'] = (expandedKeysValue) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const onCheck: TreeProps['onCheck'] = (checkedKeysValue) => {
    setCheckedKeys(checkedKeysValue as React.Key[]);
  };

  const onSelect: TreeProps['onSelect'] = (selectedKeysValue) => {
    setSelectedKeys(selectedKeysValue);
  };
  // 点击按钮时触发，展开全部节点
  const handleExpandAll = () => {
    setExpandedKeys(allKeys);
  };

  return (
    <PageContainer
      header={{
        title: false,
      }}
      className="detail-container"
    >
      <ProForm
        formRef={formRef}
        submitter={{
          searchConfig: {
            resetText: '取消',
            submitText: '保存',
          },
          onReset: () => {
            history.go(-1);
          },
          //自定义整个区域
          render: (props, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          submitButtonProps: {
            loading: addLoading || editLoading,
          },
          resetButtonProps: {
            style: {
              marginLeft: 8,
            },
          },
        }}
        onFinish={async (value) => {
          const formData = { ...value, funcListIds: checkedKeys.join(',') };
          if (isEditPage) {
            edit(formData);
          } else {
            add(formData);
          }
        }}
        request={async () => {
          const data = await queryFormData({ roleId: id }, isEditPage, getRoleByIdUsingGET);
          const checked = data?.funcListIds.split(',').map((item: string) => parseInt(item));
          setCheckedKeys(checked);
          return data;
        }}
      >
        <div style={{ display: 'none' }}>
          <ProFormText width="md" name="id" />
          <ProFormText width="md" name="funcListIds" />
        </div>
        <Row gutter={24}>
          <RKCol>
            <ProFormText name="name" label="角色名称" placeholder="请输入" rules={[requiredRule]} />
          </RKCol>
          <RKCol>
            <ProFormText
              name="description"
              label="描述"
              placeholder="请输入"
              rules={[requiredRule]}
            />
          </RKCol>
        </Row>
        <ProCard
          title="功能权限配置"
          extra={
            <Space>
              <Button type="link" onClick={handleExpandAll}>
                全部展开
              </Button>
              <Button type="link" onClick={() => setExpandedKeys([])}>
                全部收起
              </Button>
            </Space>
          }
        >
          <Tree
            checkable
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onCheck={onCheck}
            checkedKeys={checkedKeys}
            onSelect={onSelect}
            selectedKeys={selectedKeys}
            treeData={featureList as TreeDataNode[]}
          />
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default withRouteEditing(InterFaceConfig);
