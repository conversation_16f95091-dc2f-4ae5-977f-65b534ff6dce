import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { batchDelRoleUsingDELETE, getAllRoleUsingGET } from '@/services/dsp/roleController';
import { queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { history, useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import { useRef, useState } from 'react';

/**
 * 角色配置 功能权限
 */
const Roles = () => {
  const tableRef = useRef<ActionType>();
  const [selectedRows, setSelectedRows] = useState<API.Role[]>([]);

  const { run: deleteRecord } = useRequest((ids) => batchDelRoleUsingDELETE(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = (rows: API.Role[]) => {
    const ids: string[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(String(item.id)!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除“${names.join('、')}”吗?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  const columns: ProColumns<API.Role>[] = [
    {
      title: '角色名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 120,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'options',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space>
            <Button
              key="config"
              type="link"
              onClick={() => history.push(`/permissions/role/config/edit/${record.id}`)}
            >
              编辑
            </Button>
            <Button key="del" type="link" onClick={() => handleDelete([record])}>
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable
        {...defaultTableConfig}
        actionRef={tableRef}
        search={false}
        columns={columns}
        headerTitle="角色列表"
        rowSelection={{
          onChange: (selectedKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              onClick={() => history.push('/permissions/role/config/add')}
            >
              添加角色
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllRoleUsingGETParams>({ ...params }, getAllRoleUsingGET)
        }
        scroll={{ x: '100%' }}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
    </PageContainer>
  );
};

export default Roles;
