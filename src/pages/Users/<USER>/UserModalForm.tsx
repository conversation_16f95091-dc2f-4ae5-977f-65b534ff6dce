import BaseListContext from '@/components/Context/BaseListContext';
import { addUserUsingPOST, modifyUserUsingPUT } from '@/services/dsp/userController';
import { requiredRule } from '@/utils/setting';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useContext, useRef, useState } from 'react';

const UserModalForm: React.FC<ModalFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  // 判断是否是新增
  const isEdit = initialValues?.id;
  const formRef = useRef<ProFormInstance>();
  const { roleList, roleLoading } = useContext(BaseListContext);
  const initialFormValues = {
    ...initialValues,
    roleIds: initialValues?.roleIds?.split(',').map((i: string) => parseInt(i)),
  };
  const [newPassword, setNewPassword] = useState('0');

  return (
    <ModalForm
      width={736}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        const { id, userName, realName, email, roleIds, status } = value;
        const formData =
          newPassword !== '0'
            ? { ...value, roleIds: roleIds.join(',') }
            : { id, status, userName, realName, email, roleIds: roleIds.join(',') };
        const msg = isEdit ? await modifyUserUsingPUT(formData) : await addUserUsingPOST(formData);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(value);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={isEdit ? initialFormValues : {}}
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      onValuesChange={(values) => {
        if (values.password) {
          setNewPassword(values.password);
        }
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
        <ProFormText name="status" placeholder="请输入" />
      </div>
      <ProForm.Group>
        <ProFormText
          width="md"
          name="userName"
          label="用户名"
          placeholder="请输入"
          fieldProps={{
            autoComplete: 'none',
          }}
          rules={[requiredRule]}
        />
        <ProFormText.Password width="md" name="password" label="密码" rules={[requiredRule]} />
        <ProFormText
          width="md"
          name="realName"
          label="真实姓名"
          placeholder="请输入真实姓名"
          rules={[requiredRule]}
        />
        <ProFormText
          width="md"
          name="email"
          label="邮箱"
          rules={[
            requiredRule,
            {
              type: 'email',
              message: '格式不正确',
            },
          ]}
        />
        <ProFormSelect
          mode="multiple"
          width="md"
          name="roleIds"
          rules={[requiredRule]}
          label="角色"
          fieldProps={{
            loading: roleLoading,
            showSearch: true,
            fieldNames: {
              value: 'id',
              label: 'description',
            },
            filterOption: true,
            optionFilterProp: 'label',
          }}
          options={roleList as DefaultOptionType[]}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default UserModalForm;
