import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import BaseListContext from '@/components/Context/BaseListContext';
import { useRoleList } from '@/components/hooks/useRoleList';
import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { USER_STATUS } from '@/enums';
import {
  batchDeleteUsingPATCH,
  getAllUserUsingGET,
  modifyUserUsingPUT,
} from '@/services/dsp/userController';
import { option2enum, queryPagingTable } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import UserModalForm from './components/UserModalForm';

const Users: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.User[]>([]);
  const [initialValues, setInitialValues] = useState<API.User | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [rowId, setRowId] = useState<number>();
  const { roleList, loading: roleLoading } = useRoleList();

  const onEdit = (record: API.User) => {
    setModalVisit(true);
    setInitialValues(record);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDeleteUsingPATCH(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = async (rows: API.User[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.userName!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除用户“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };

  // // 批量禁用
  // const { run: disableRecords } = useRequest((ids) => modifyUserUsingPUT(ids), {
  //   manual: true,
  //   onSuccess: () => {
  //     message.success('禁用成功');
  //     tableRef.current?.reloadAndRest?.();
  //   },
  // });
  // const handleBatchDisabled = async (key: string, selectedRows: API.User[]) => {
  //   const ids: number[] = [];
  //   const names: string[] = [];
  //   selectedRows
  //     .filter((row) => row.status !== 1)
  //     .forEach((item) => {
  //       ids.push(item.id!);
  //       names.push(item.userName!);
  //     });
  //   Modal.confirm({
  //     title: '确认禁用',
  //     content: `您确定要禁用用户“${names.join('、')}”吗？`,
  //     okText: '确认',
  //     cancelText: '取消',
  //     onOk: async () => {
  //       disableRecords(ids);
  //     },
  //   });
  // };
  //禁用or启用
  const { run: disableRecord, loading } = useRequest((row) => modifyUserUsingPUT(row), {
    manual: true,
    onSuccess: (data, params) => {
      const text = params.at(0).status === 0 ? '启用' : '禁用';
      message.success(`${text}成功`);
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDisabled = async (record: API.User) => {
    const text = record.status === 0 ? '禁用' : '启用';
    Modal.confirm({
      title: `确认“${text}”`,
      content: `您确定要${text}用户“${record.userName}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        const status = record.status === 0 ? 1 : 0;
        const row = { id: record.id, status };
        disableRecord(row);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.User>[] = [
    {
      title: '姓名',
      dataIndex: 'realName',
      fixed: 'left',
      width: 120,
      ellipsis: true,
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      width: 120,
      ellipsis: true,
    },
    {
      title: '企业账号邮箱',
      dataIndex: 'email',
      width: 120,
      ellipsis: true,
    },
    {
      title: '角色',
      dataIndex: 'roleIds',
      width: 120,
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        loading: roleLoading,
        showSearch: true,
        fieldNames: {
          value: 'id',
          label: 'description',
        },
        options: roleList as DefaultOptionType[],
      },
      renderText: (text) => text?.split(',').map((i: string) => parseInt(i)),
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: option2enum(USER_STATUS),
      width: 120,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <Button
              type="link"
              className="ant-btn-link-table"
              key="disabled"
              onClick={() => handleDisabled(record)}
              loading={loading && rowId === record.id}
            >
              {record.status === 0 ? '禁用' : '启用'}
            </Button>
            <a key="edit" onClick={() => onEdit(record)}>
              编辑
            </a>
            <a key="del" onClick={() => handleDelete([record])}>
              删除
            </a>
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.User>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="用户列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建用户
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUserUsingGETParams>({ ...params }, getAllUserUsingGET)
        }
      />
      <OperateFooterToolbar
        selectedRows={selectedRows}
        onDelete={handleDelete}
        // onOperation={handleBatchDisabled}
        // actions={[{ key: 'disabled', label: '批量禁用' }]}
      />
      <BaseListContext.Provider value={{ roleList, roleLoading }}>
        <UserModalForm
          initialValues={initialValues}
          open={modalVisit}
          onOpenChange={(visible) => {
            setModalVisit(visible);
          }}
          onFinish={async () => {
            tableRef.current?.reload();
          }}
        />
      </BaseListContext.Provider>
    </PageContainer>
  );
};

export default Users;
