import { useDBList } from '@/components/hooks/useDBList';
import SchemaTable from '@/components/SchemaTableTree/index';
import { addUsingPOST1, editUsingPUT1 } from '@/services/dsp/dataListenerController';
import { requiredRule } from '@/utils/setting';
import {
  DrawerForm,
  DrawerFormProps,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Divider, message, TreeProps } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import React, { useRef } from 'react';

const DataCollectDrawerForm: React.FC<DrawerFormProps> = ({
  open,
  onOpenChange,
  initialValues,
  onFinish,
}) => {
  const isEdit = initialValues?.id;
  const initialCheckedKeys = initialValues?.initCheckedKeys;
  const formRef = useRef<ProFormInstance>();
  const tableNamesRef = useRef<string[]>([]);
  const { dbList, loading: dbListLoading } = useDBList();

  return (
    <DrawerForm<API.DataListenerConfig>
      width={460}
      title={isEdit ? '编辑' : '新建'}
      formRef={formRef}
      open={open}
      onOpenChange={onOpenChange}
      onFinish={async (value) => {
        let tableNames = '';
        if (tableNamesRef.current.length > 0) {
          tableNames = tableNamesRef.current.toString();
        } else {
          tableNames = initialValues?.tableNames.split(',').toString();
        }
        const formData = {
          ...value,
          status: isEdit ? initialValues?.status : 0,
          tableNames: tableNames,
        };
        const msg = isEdit ? await editUsingPUT1(formData) : await addUsingPOST1(formData);
        const success = msg.code === 100;
        if (success) {
          message.success('操作成功!');
          onFinish?.(formData);
        }
        return success;
      }}
      autoFocusFirstInput
      initialValues={initialValues}
      drawerProps={{
        destroyOnClose: true,
        maskClosable: false,
      }}
    >
      {/* 不需要展示，只是为了form传值 */}
      <div style={{ display: 'none' }}>
        <ProFormText name="id" label="id" placeholder="请输入" />
      </div>
      <ProFormText
        name="name"
        label="名称"
        placeholder="请输入"
        fieldProps={{
          autoComplete: 'none',
        }}
        rules={[requiredRule]}
        disabled={isEdit}
      />
      <ProFormSelect
        name="dbId"
        label="数据库"
        rules={[requiredRule]}
        fieldProps={{
          allowClear: false,
          loading: dbListLoading,
          showSearch: true,
          fieldNames: {
            value: 'id',
            label: 'dbName',
          },
        }}
        options={dbList as DefaultOptionType[]}
        disabled={isEdit}
      />
      {/* 树型结构 */}
      <ProFormDependency key="dbId" name={['dbId']} ignoreFormListField>
        {({ dbId }) => {
          if (dbId) {
            return (
              <>
                <Divider type="horizontal"></Divider>
                <SchemaTable
                  dbId={dbId}
                  initCheckedKeys={initialCheckedKeys}
                  onCheck={(values: TreeProps['checkedKeys']) => {
                    tableNamesRef.current = (
                      values as {
                        checked: string[];
                        halfChecked: string[];
                      }
                    ).checked?.filter((item) => (item as string).indexOf('.'));
                  }}
                />
              </>
            );
          }
        }}
      </ProFormDependency>
    </DrawerForm>
  );
};

export default DataCollectDrawerForm;
