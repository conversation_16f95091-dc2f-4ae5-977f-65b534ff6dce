import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import React, { useRef, useState } from 'react';

import OperateFooterToolbar from '@/components/OperateFooterToolbar';
import { EXEC_STATUS } from '@/enums';
import {
  batchDelUsingDELETE1,
  execSyncUsingPOST,
  getAllUsingGET2,
} from '@/services/dsp/dataListenerController';
import { option2enum, queryPagingTable, tablesTransform } from '@/utils';
import { defaultTableConfig } from '@/utils/setting';
import { PlusOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import DataCollectDrawerForm from './components/CollectConfigDrawerForm';

const DataCollect: React.FC = () => {
  const tableRef = useRef<ActionType | undefined>();
  const [selectedRows, setSelectedRows] = useState<API.DataListenerConfig[]>([]);
  const [initialValues, setInitialValues] = useState<API.DataListenerConfig | undefined>();
  const [modalVisit, setModalVisit] = useState(false);
  const [rowId, setRowId] = useState<number>();

  const onEdit = (record: API.DataListenerConfig) => {
    const { tableNames } = record;
    const records = {
      ...record,
      initCheckedKeys: tablesTransform(tableNames),
    };
    setInitialValues(records);
    setModalVisit(true);
  };

  // 删除
  const { run: deleteRecord } = useRequest((ids) => batchDelUsingDELETE1(ids), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDelete = async (rows: API.DataListenerConfig[]) => {
    const ids: number[] = [];
    const names: string[] = [];
    rows.forEach((item) => {
      ids.push(item.id!);
      names.push(item.name!);
    });
    Modal.confirm({
      title: '确认删除',
      content: `您确定要删除采集“${names.join('、')}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        deleteRecord(ids);
      },
    });
  };
  //停止or启动
  const { run: disableRecord, loading } = useRequest((row) => execSyncUsingPOST(row), {
    manual: true,
    onSuccess: (data, params) => {
      const text = params.at(0).status === 0 ? '启动' : '停止';
      message.success(`${text}成功`);
      setRowId(undefined);
      tableRef.current?.reloadAndRest?.();
    },
  });
  const handleDisabled = async (record: API.DataListenerConfig) => {
    const text = record.status === 0 ? '停止' : '启动';
    Modal.confirm({
      title: `确认“${text}”`,
      content: `您确定要${text}采集“${record.name}”吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        setRowId(record.id);
        const status = record.status === 0 ? 1 : 0;
        const row = { ...record, status };
        disableRecord(row);
      },
    });
  };

  // 表格
  const columns: ProColumns<API.DataListenerConfig>[] = [
    {
      title: '名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 150,
      ellipsis: true,
    },
    {
      title: '数据库名称',
      dataIndex: 'dbName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '数据库类型',
      dataIndex: 'dbType',
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      width: 150,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: option2enum(EXEC_STATUS),
      width: 100,
      ellipsis: true,
    },
    {
      title: '操作',
      width: 150,
      key: 'option',
      fixed: 'right',
      valueType: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            {record.status !== 0 && (
              <a key="edit" onClick={() => onEdit(record)}>
                编辑
              </a>
            )}
            <Button
              type="link"
              className="ant-btn-link-table"
              key="disabled"
              onClick={() => handleDisabled(record)}
              loading={loading && rowId === record.id}
            >
              {record.status === 0 ? '停止' : '启动'}
            </Button>
            {record.status !== 0 && (
              <a key="del" onClick={() => handleDelete([record])}>
                删除
              </a>
            )}
          </Space>
        );
      },
    },
  ];
  return (
    <PageContainer
      header={{
        title: false,
      }}
    >
      <ProTable<API.DataListenerConfig>
        {...defaultTableConfig}
        actionRef={tableRef}
        rowSelection={{
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: record?.status !== 1,
          }),
        }}
        options={{ density: false, setting: false, search: false }}
        columns={columns}
        headerTitle="采集列表"
        toolbar={{
          actions: [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setModalVisit(true);
                setInitialValues(undefined);
              }}
            >
              新建采集
            </Button>,
          ],
        }}
        request={async (params) =>
          queryPagingTable<API.getAllUsingGET2Params>({ ...params }, getAllUsingGET2)
        }
        polling={5000}
      />
      <OperateFooterToolbar selectedRows={selectedRows} onDelete={handleDelete} />
      {/* 新建/编辑模版 */}
      <DataCollectDrawerForm
        initialValues={initialValues}
        open={modalVisit}
        onOpenChange={(visible) => {
          setModalVisit(visible);
        }}
        onFinish={async () => {
          tableRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};

export default DataCollect;
