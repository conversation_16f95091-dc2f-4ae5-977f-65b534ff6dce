declare type AntTableParams = {
  pageSize?: number;
  current?: number;
  keyword?: string;
};

declare type BaseResponse = {
  code: number;
  data: Record<string, any>;
  message: string;
};

declare type AuthProps = {
  isAdmin: boolean;
};

declare type BaseListRequest = {
  pageSize?: number;
  pageNum?: number;
  keyword?: string;
};

declare module DSP_API {
  //日志
  type SysLog = {
    operation?: string;
    id?: number;
    servletPath?: string;
    operatStatus?: string;
    parameters?: string;
    errorMessage?: string;
    userName?: string;
    createTime?: string;
  };
  //访问审计
  type LoginAuditData = {
    id: number;
    actionName: string;
    dbId: number;
    loginTime: string;
    logoffTime: string;
    objName: string;
    osUserName: string;
    sqlBind: string;
    sqlText: string;
    terminal: string;
    userHost: string;
    userName: string;
  };
  //手动脱敏详情
  type ManualDetailsData = {
    id: number;
    endTime: string;
    exception: string;
    filePath: string;
    manualMaskConfigId: number;
    startTime: string;
    status: number;
  };
}
