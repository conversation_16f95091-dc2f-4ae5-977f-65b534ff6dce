import Footer from '@/components/Footer';
import RightContent from '@/components/RightContent';
import { Settings as LayoutSettings } from '@ant-design/pro-components';
import { history, RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';

// import { FormInstance } from 'antd';
// import { createRef } from 'react';
import UnAccessible from '../src/pages/403';
import { getUserByTokenUsingGET } from './services/dsp/userController';
import { BASE_URL } from './utils/setting';
// const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.User;
  loading?: boolean;
  fetchUserInfo?: () => API.User;
}> {
  const token = localStorage.getItem('RKLINK_DSP_TOKEN') || '';
  const fetchUserInfo = async () => {
    try {
      const userInfo = await getUserByTokenUsingGET({ token }, { skipErrorHandler: true });
      const { user } = userInfo || {};
      return user;
    } catch (error) {
      history.push(loginPath);
    }
    return {};
  };
  // 如果不是登录页面，执行
  if (history.location.pathname !== loginPath) {
    const currentUser = await fetchUserInfo();
    return {
      currentUser,
      settings: defaultSettings,
    };
  }

  return {
    settings: defaultSettings,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  // const formRef = createRef<FormInstance>();

  return {
    logo: false,
    rightContentRender: () => <RightContent />,
    footerRender: () => <Footer />,
    onPageChange: () => {
      // 如果没有登录，重定向到 login
      const token = localStorage.getItem('RKLINK_DSP_TOKEN');
      if (!token && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    unAccessible: <UnAccessible />,
    token: {
      pageContainer: {
        paddingBlockPageContainerContent: 24,
        paddingInlinePageContainerContent: 24,
      },
      sider: {
        colorMenuBackground: '#fff',
        colorTextMenuSelected: '#13c2c2',
        colorTextMenuActive: '#13c2c2',
        colorTextMenuItemHover: '#13c2c2',
      },
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  // timeout: 30000,
  timeout: 60000,
  headers: { 'X-Requested-With': 'XMLHttpRequest' },
  baseURL: BASE_URL,
  ...errorConfig,
};
