// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加敏感信息识别规则 POST /api/recognizeRule/addRecognizeRule */
export async function addRecognizeRuleUsingPOST(
  body: API.RecognizeRule,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/recognizeRule/addRecognizeRule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除敏感信息识别规则 DELETE /api/recognizeRule/batchDelRecognizeRule */
export async function batchDelRecognizeRuleUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/recognizeRule/batchDelRecognizeRule', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除敏感信息识别规则 DELETE /api/recognizeRule/delRecognizeRule */
export async function delRecognizeRuleUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delRecognizeRuleUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/recognizeRule/delRecognizeRule', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取敏感信息识别规则 GET /api/recognizeRule/getAllRecognizeRule */
export async function getAllRecognizeRuleUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllRecognizeRuleUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/recognizeRule/getAllRecognizeRule', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改敏感信息识别规则 POST /api/recognizeRule/modifyRecognizeRule */
export async function modifyRecognizeRuleUsingPOST(
  body: API.RecognizeRule,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/recognizeRule/modifyRecognizeRule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
