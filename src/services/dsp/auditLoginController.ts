// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 分页查询登录审计信息 POST /api/auditLogin/getAll */
export async function getAllUsingPOST1(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/auditLogin/getAll', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
