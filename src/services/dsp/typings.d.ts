declare namespace API {
  type Alarm = {
    id?: number;
    sysRemain?: string;
    userName?: string;
  };

  type ApprovalList = {
    approvalStatus?: number;
    auditorId?: number;
    createTime?: string;
    description?: string;
    funcListId?: number;
    id?: number;
    listerId?: number;
    processingId?: number;
    updateTime?: string;
  };

  type AuditConfig = {
    createTime?: string;
    id?: number;
    listenerId?: number;
    name?: string;
    status?: number;
    syncDataId?: number;
    tables?: string;
  };

  type AuditData = {
    afterData?: string;
    auditConfigId?: number;
    auditTime?: string;
    auditType?: string;
    beforeData?: string;
    createTime?: string;
    dataId?: string;
    ddlStr?: string;
    id?: number;
    listenerId?: number;
    syncDataId?: number;
    tableName?: string;
    userName?: string;
  };

  type DataCollection = {
    bootstrapServersConfig?: string;
    createTime?: string;
    groupIdConfig?: string;
    id?: number;
    name?: string;
    password?: string;
    topicNames?: number;
    updateTime?: string;
    username?: string;
  };

  type DataListenerConfig = {
    createTime?: string;
    dbId?: number;
    excludeColumn?: string;
    id?: number;
    messagekeycolumn?: string;
    name?: string;
    racNodes?: string;
    status?: number;
    tableNames?: string;
  };

  type DataSyncConfig = {
    createTime?: string;
    dataCollectionId?: number;
    exception?: string;
    id?: number;
    lastOffsets?: string;
    lastStatus?: number;
    lastTime?: string;
    name?: string;
    startTime?: string;
    status?: number;
    tableNames?: string;
    targetDbId?: number;
    topicName?: string;
    updateTime?: string;
  };

  type DB = {
    className?: string;
    createTime?: string;
    dbName?: string;
    dbPassword?: string;
    dbType?: string;
    dbUser?: string;
    enType?: string;
    host?: string;
    id?: number;
    lastAuditTime?: string;
    pdbName?: string;
    port?: string;
    racNodes?: string;
    seName?: string;
    version?: string;
  };

  type delAuditUsingDELETEParams = {
    /** id */
    id: number;
  };

  type delConfigUsingDELETE1Params = {
    /** id */
    id: number;
  };

  type delConfigUsingDELETE2Params = {
    /** id */
    id: number;
  };

  type delConfigUsingDELETE3Params = {
    /** id */
    id: number;
  };

  type delConfigUsingDELETE4Params = {
    /** id */
    id: number;
  };

  type delConfigUsingDELETEParams = {
    /** id */
    id: number;
  };

  type delDbUsingDELETEParams = {
    /** id */
    id: number;
  };

  type delFuncListUsingDELETEParams = {
    /** funcListId */
    funcListId: number;
  };

  type delManualMaskUsingDELETEParams = {
    /** manualMaskId */
    manualMaskId: number;
  };

  type delMaskConfigUsingDELETEParams = {
    /** maskConfigId */
    maskConfigId: number;
  };

  type delPermissionsUsingDELETEParams = {
    /** permissionsId */
    permissionsId: number;
  };

  type delRecognizeRuleUsingDELETEParams = {
    /** recognizeRuleId */
    recognizeRuleId: number;
  };

  type delRoleUsingDELETEParams = {
    /** roleId */
    roleId: number;
  };

  type delScanSensitiveUsingDELETEParams = {
    /** scanSensitiveId */
    scanSensitiveId: number;
  };

  type delScheduleUsingDELETEParams = {
    /** scheduledJobId */
    scheduledJobId: number;
  };

  type delStrategyConfigUsingDELETEParams = {
    /** id */
    id: number;
  };

  type delUserUsingDELETEParams = {
    /** id */
    id: number;
  };

  type downloadLocalUsingGETParams = {
    /** path */
    path: string;
  };

  type Emails = {
    id?: number;
    security?: string;
    serverPort?: string;
    smtpPassword?: string;
    smtpServer?: string;
    smtpUser?: string;
  };

  type executeDataSyncUsingGETParams = {
    /** dataSyncConfigId */
    dataSyncConfigId: number;
  };

  type executeMaskConfigUsingPOSTParams = {
    /** maskConfigId */
    maskConfigId: number;
  };

  type fetchColumnsUsingGETParams = {
    /** dbId */
    dbId: number;
    /** tableName */
    tableName: string;
  };

  type fetchSchemaAndTablesUsingGETParams = {
    /** dbId */
    dbId: number;
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
    /** whereCluster */
    whereCluster?: string;
  };

  type fetchSchemasUsingGETParams = {
    /** dbId */
    dbId: number;
    /** schemaName */
    schemaName?: string;
  };

  type fetchTablesUsingGETParams = {
    /** dbId */
    dbId: number;
    /** schemaName */
    schemaName: string;
  };

  type FuncList = {
    createTime?: string;
    id?: number;
    moduleName?: string;
    moduleNameEng?: string;
    operationType?: string;
    parentId?: number;
    updateTime?: string;
  };

  type getAllApprovalListUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** userId */
    userId: number;
  };

  type getAllFuncListUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllManualMaskUsingGET1Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** manualMaskConfigId */
    manualMaskConfigId: number;
  };

  type getAllManualMaskUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllMaskConfigInfoUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** maskConfigId */
    maskConfigId: number;
  };

  type getAllMaskConfigUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllPermissionsUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllRecognizeRuleUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllRoleUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllScanSensitiveInfoUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** scanSensitiveId */
    scanSensitiveId: number;
    /** tableName */
    tableName?: string;
  };

  type getAllScanSensitiveUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllStrategyUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUserUsingGETParams = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type getAllUsingGET10Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET1Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET2Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET3Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET4Params = {
    /** pageNum */
    pageNum: number;
    /** pageSize */
    pageSize: number;
  };

  type getAllUsingGET5Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET6Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET7Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET8Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGET9Params = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getAllUsingPOSTParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getCompareResultUsingPOSTParams = {
    /** jobId */
    jobId: string;
  };

  type getDataSyncConfigInfoUsingGETParams = {
    /** dataSyncConfigId */
    dataSyncConfigId: number;
  };

  type getDataSyncConfigTableChangeInfoByFilterUsingGETParams = {
    /** dataSyncConfigId */
    dataSyncConfigId: number;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** filter */
    filter?: Record<string, any>;
  };

  type getDataSyncConfigTableChangeInfoUsingGETParams = {
    /** dataSyncConfigId */
    dataSyncConfigId: number;
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
  };

  type getKafkaContentsUsingGETParams = {
    /** dataSyncConfigInfoId */
    dataSyncConfigInfoId: number;
    /** topic */
    topic: string;
    /** partition */
    partition: number;
    /** offset */
    offset?: number;
  };

  type getMaskConfigUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** approvalListStatus */
    approvalListStatus?: number;
    /** userId */
    userId?: string;
  };

  type getMaskRuleUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** maskConfigId */
    maskConfigId: number;
  };

  type getRoleByIdUsingGETParams = {
    /** roleId */
    roleId: number;
  };

  type getTopicPartitionsUsingGETParams = {
    /** dataSyncConfigInfoId */
    dataSyncConfigInfoId: number;
    /** topic */
    topic: string;
  };

  type getUserByTokenUsingGETParams = {
    /** token */
    token?: string;
  };

  type InputStream = true;

  type InputStreamResource = {
    description?: string;
    file?: string;
    filename?: string;
    inputStream?: InputStream;
    open?: boolean;
    readable?: boolean;
    uri?: string;
    url?: string;
  };

  type ManualMaskConfig = {
    createTime?: string;
    dbId?: number;
    filePath?: string;
    id?: number;
    lastStatus?: number;
    lastTime?: string;
    listenerId?: number;
    manualType?: string;
    maskName?: string;
    sqlStr?: string;
    status?: number;
    strategy?: string;
    tables?: string;
  };

  type MaskConfig = {
    createTime?: string;
    dbId?: string;
    excludeFields?: string;
    filePath?: string;
    id?: number;
    lastStatus?: string;
    lastTime?: string;
    maskName?: string;
    scanSensitiveId?: number;
    status?: string;
    tableNames?: string;
    targetType?: string;
    updateTime?: string;
    userId?: number;
    waterMark?: number;
  };

  type MaskConfigInfo = {
    createTime?: string;
    endTime?: string;
    exception?: string;
    filePath?: string;
    id?: number;
    maskConfigId?: number;
    masked?: number;
    startTime?: string;
    status?: number;
    total?: number;
    updateTime?: string;
  };

  type MaskRuleVo = {
    codeName?: string;
    coverChar?: string;
    createTime?: string;
    defaultMask?: string;
    effectRange?: string;
    fieldName?: string;
    id?: number;
    isNumber?: number;
    keepOffset?: number;
    maskConfigId?: number;
    maskType?: string;
    recognizeRuleId?: number;
    reg?: string;
    ruleName?: string;
    salt?: number;
    scanSensitiveInfoId?: number;
    scanSensitiveName?: string;
    schemaName?: string;
    status?: number;
    tableName?: string;
    updateTime?: string;
  };

  type ModelAndView = {
    empty?: boolean;
    model?: Record<string, any>;
    modelMap?: Record<string, any>;
    reference?: boolean;
    status?:
      | 'ACCEPTED'
      | 'ALREADY_REPORTED'
      | 'BAD_GATEWAY'
      | 'BAD_REQUEST'
      | 'BANDWIDTH_LIMIT_EXCEEDED'
      | 'CHECKPOINT'
      | 'CONFLICT'
      | 'CONTINUE'
      | 'CREATED'
      | 'DESTINATION_LOCKED'
      | 'EXPECTATION_FAILED'
      | 'FAILED_DEPENDENCY'
      | 'FORBIDDEN'
      | 'FOUND'
      | 'GATEWAY_TIMEOUT'
      | 'GONE'
      | 'HTTP_VERSION_NOT_SUPPORTED'
      | 'IM_USED'
      | 'INSUFFICIENT_SPACE_ON_RESOURCE'
      | 'INSUFFICIENT_STORAGE'
      | 'INTERNAL_SERVER_ERROR'
      | 'I_AM_A_TEAPOT'
      | 'LENGTH_REQUIRED'
      | 'LOCKED'
      | 'LOOP_DETECTED'
      | 'METHOD_FAILURE'
      | 'METHOD_NOT_ALLOWED'
      | 'MOVED_PERMANENTLY'
      | 'MOVED_TEMPORARILY'
      | 'MULTIPLE_CHOICES'
      | 'MULTI_STATUS'
      | 'NETWORK_AUTHENTICATION_REQUIRED'
      | 'NON_AUTHORITATIVE_INFORMATION'
      | 'NOT_ACCEPTABLE'
      | 'NOT_EXTENDED'
      | 'NOT_FOUND'
      | 'NOT_IMPLEMENTED'
      | 'NOT_MODIFIED'
      | 'NO_CONTENT'
      | 'OK'
      | 'PARTIAL_CONTENT'
      | 'PAYLOAD_TOO_LARGE'
      | 'PAYMENT_REQUIRED'
      | 'PERMANENT_REDIRECT'
      | 'PRECONDITION_FAILED'
      | 'PRECONDITION_REQUIRED'
      | 'PROCESSING'
      | 'PROXY_AUTHENTICATION_REQUIRED'
      | 'REQUESTED_RANGE_NOT_SATISFIABLE'
      | 'REQUEST_ENTITY_TOO_LARGE'
      | 'REQUEST_HEADER_FIELDS_TOO_LARGE'
      | 'REQUEST_TIMEOUT'
      | 'REQUEST_URI_TOO_LONG'
      | 'RESET_CONTENT'
      | 'SEE_OTHER'
      | 'SERVICE_UNAVAILABLE'
      | 'SWITCHING_PROTOCOLS'
      | 'TEMPORARY_REDIRECT'
      | 'TOO_EARLY'
      | 'TOO_MANY_REQUESTS'
      | 'UNAUTHORIZED'
      | 'UNAVAILABLE_FOR_LEGAL_REASONS'
      | 'UNPROCESSABLE_ENTITY'
      | 'UNSUPPORTED_MEDIA_TYPE'
      | 'UPGRADE_REQUIRED'
      | 'URI_TOO_LONG'
      | 'USE_PROXY'
      | 'VARIANT_ALSO_NEGOTIATES';
    view?: View;
    viewName?: string;
  };

  type Permissions = {
    createTime?: string;
    funcListId?: number;
    id?: number;
    requestMethod?: string;
    status?: string;
    updateTime?: string;
    url?: string;
  };

  type RecognizeRule = {
    codeName?: string;
    contentLength?: string;
    createTime?: string;
    defaultMaskType?: string;
    description?: string;
    id?: number;
    isNumber?: string;
    reg?: string;
    ruleName?: string;
    status?: number;
    updateTime?: string;
  };

  type Result = {
    code?: number;
    data?: Record<string, any>;
    message?: string;
  };

  type Role = {
    createTime?: string;
    description?: string;
    funcListIds?: string;
    id?: number;
    name?: string;
    updateTime?: string;
  };

  type ScanSensitive = {
    createTime?: string;
    dataSourceType?: string;
    dbId?: string;
    exception?: string;
    filePath?: string;
    id?: number;
    recognizeRulesIds?: string;
    scanName?: string;
    sqlStr?: string;
    status?: number;
    tables?: string;
    updateTime?: string;
  };

  type ScanSensitiveInfo = {
    createTime?: string;
    fieldName?: string;
    id?: number;
    recognizeRuleId?: number;
    scanSensitiveId?: number;
    schemaName?: string;
    status?: string;
    tableName?: string;
    updateTime?: string;
  };

  type scanSensitiveUsingPOSTParams = {
    /** scanSensitiveId */
    scanSensitiveId: number;
  };

  type ScheduledJob = {
    createTime?: string;
    cronExpression?: string;
    dbId?: number;
    jobId?: number;
    jobKey?: string;
    jobName?: string;
    lastAuditTime?: number;
    status?: number;
    taskExplain?: string;
  };

  type searchDbByIdUsingGETParams = {
    /** id */
    id: number;
  };

  type ServerConfig = {
    cntServers?: string;
    id?: number;
    servers?: string;
  };

  type Strategy = {
    codeName?: string;
    id?: number;
    status?: string;
    strategyName?: string;
  };

  type StrategyConfig = {
    configName?: string;
    createTime?: string;
    id?: number;
    listenerId?: number;
    status?: number;
    syncDataId?: number;
    tables?: string;
    targetDbId?: number;
  };

  type SyncConfig = {
    createTime?: string;
    excludeColumn?: string;
    id?: number;
    listenerId?: number;
    name?: string;
    status?: number;
    syncDataId?: number;
    tables?: string;
    targetDbId?: number;
  };

  type SystemConfigs = {
    createTime?: string;
    id?: number;
    systemKey?: string;
    systemName?: string;
    systemValue?: string;
    updateTime?: string;
  };

  type testLinkUsingGETParams = {
    /** link */
    link: string;
  };

  type updateSyncConfigUsingPUTParams = {
    /** 修改后的json字符串 */
    json: string;
  };

  type User = {
    createTime?: string;
    email?: string;
    id?: number;
    password?: string;
    realName?: string;
    roleIds?: string;
    status?: number;
    updateTime?: string;
    userName?: string;
  };

  type View = {
    contentType?: string;
  };

  type viewDataUsingGETParams = {
    /** pageNum */
    pageNum?: number;
    /** pageSize */
    pageSize?: number;
    /** scanSensitiveInfoId */
    scanSensitiveInfoId: number;
  };

  type viewGeneralUsingPOSTParams = {
    /** scanSensitiveId */
    scanSensitiveId: number;
  };
}
