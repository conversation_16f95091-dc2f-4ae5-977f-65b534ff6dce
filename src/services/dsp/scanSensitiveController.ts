// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加敏感信息扫描信息 POST /api/scanSensitive/addScanSensitive */
export async function addScanSensitiveUsingPOST(
  body: API.ScanSensitive,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitive/addScanSensitive', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除敏感信息扫描 DELETE /api/scanSensitive/batchDelScanSensitive */
export async function batchDelScanSensitiveUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitive/batchDelScanSensitive', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除敏感信息扫描信息 DELETE /api/scanSensitive/delScanSensitive */
export async function delScanSensitiveUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delScanSensitiveUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitive/delScanSensitive', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取敏感信息识别规则 GET /api/scanSensitive/getAllScanSensitive */
export async function getAllScanSensitiveUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllScanSensitiveUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitive/getAllScanSensitive', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改敏感信息扫描信息 POST /api/scanSensitive/modifyScanSensitive */
export async function modifyScanSensitiveUsingPOST(
  body: API.ScanSensitive,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitive/modifyScanSensitive', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 扫描敏感信息 POST /api/scanSensitive/scanSensitive */
export async function scanSensitiveUsingPOST(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.scanSensitiveUsingPOSTParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitive/scanSensitive', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 上传文件 POST /api/scanSensitive/uploadFile */
export async function uploadFileUsingPOST1(body: string, options?: { [key: string]: any }) {
  return request<API.Result>('/api/scanSensitive/uploadFile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 扫描结果总览 POST /api/scanSensitive/viewGeneral */
export async function viewGeneralUsingPOST(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewGeneralUsingPOSTParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitive/viewGeneral', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
