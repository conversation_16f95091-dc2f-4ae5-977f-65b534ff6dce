// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：
import * as alarmController from './alarmController';
import * as approvalListController from './approvalListController';
import * as auditConfigController from './auditConfigController';
import * as auditDataController from './auditDataController';
import * as auditLoginController from './auditLoginController';
import * as basicErrorController from './basicErrorController';
import * as dataCollectionController from './dataCollectionController';
import * as dataListenerController from './dataListenerController';
import * as dataSyncConfigController from './dataSyncConfigController';
import * as dataSyncConfigInfoController from './dataSyncConfigInfoController';
import * as dataSyncConfigTableChangeController from './dataSyncConfigTableChangeController';
import * as dbController from './dbController';
import * as emailsController from './emailsController';
import * as funcListController from './funcListController';
import * as logController from './logController';
import * as manualMaskConfigController from './manualMaskConfigController';
import * as manualMaskHistoryController from './manualMaskHistoryController';
import * as maskConfigController from './maskConfigController';
import * as maskConfigInfoController from './maskConfigInfoController';
import * as maskDownloadController from './maskDownloadController';
import * as maskRuleController from './maskRuleController';
import * as permissionsController from './permissionsController';
import * as recognizeRuleController from './recognizeRuleController';
import * as roleController from './roleController';
import * as scanSensitiveController from './scanSensitiveController';
import * as scanSensitiveInfoController from './scanSensitiveInfoController';
import * as scheduledJobController from './scheduledJobController';
import * as serverConfigController from './serverConfigController';
import * as strategyConfigController from './strategyConfigController';
import * as strategyController from './strategyController';
import * as syncConfigController from './syncConfigController';
import * as systemConfigsController from './systemConfigsController';
import * as userController from './userController';
export default {
  alarmController,
  approvalListController,
  auditConfigController,
  auditDataController,
  auditLoginController,
  basicErrorController,
  dataCollectionController,
  dataListenerController,
  dataSyncConfigController,
  dataSyncConfigInfoController,
  dataSyncConfigTableChangeController,
  dbController,
  emailsController,
  funcListController,
  logController,
  manualMaskConfigController,
  manualMaskHistoryController,
  maskConfigController,
  maskConfigInfoController,
  maskDownloadController,
  maskRuleController,
  permissionsController,
  recognizeRuleController,
  roleController,
  scanSensitiveController,
  scanSensitiveInfoController,
  scheduledJobController,
  serverConfigController,
  strategyConfigController,
  strategyController,
  syncConfigController,
  systemConfigsController,
  userController,
};
