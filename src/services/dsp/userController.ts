// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** addUser POST /api/user/add */
export async function addUserUsingPOST(body: API.User, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** batchDelete PATCH /api/user/batchDelete */
export async function batchDeleteUsingPATCH(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/batchDelete', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** delUser DELETE /api/user/delUser/${param0} */
export async function delUserUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delUserUsingDELETEParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/user/delUser/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** getAllUser GET /api/user/getAll */
export async function getAllUserUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUserUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/user/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** getUserByToken GET /api/user/getUserByToken */
export async function getUserByTokenUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getUserByTokenUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/user/getUserByToken', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** login POST /api/user/login */
export async function loginUsingPOST(body: API.User, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** modifyUser PUT /api/user/modifyUser */
export async function modifyUserUsingPUT(body: API.User, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/modifyUser', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
