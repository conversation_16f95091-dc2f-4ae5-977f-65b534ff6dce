// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加数据监听配置 添加配置 POST /api/dataListener/addConfig */
export async function addUsingPOST1(
  body: API.DataListenerConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataListener/addConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除数据监听配置 DELETE /api/dataListener/batchDel */
export async function batchDelUsingDELETE1(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/dataListener/batchDel', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除数据监听配置 删除配置 DELETE /api/dataListener/delConfig/${param0} */
export async function delConfigUsingDELETE1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delConfigUsingDELETE1Params,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/dataListener/delConfig/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 开启/关闭监听 POST /api/dataListener/exec */
export async function execSyncUsingPOST(
  body: API.DataListenerConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataListener/exec', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取所有数据监听配置 GET /api/dataListener/getAll */
export async function getAllUsingGET2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET2Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataListener/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改数据监听配置 PUT /api/dataListener/modifyConfig */
export async function editUsingPUT1(
  body: API.DataListenerConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataListener/modifyConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
