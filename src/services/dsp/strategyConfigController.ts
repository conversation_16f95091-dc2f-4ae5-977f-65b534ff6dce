// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加实时脱敏配置 POST /api/strategyConfig/addStrategyConfig */
export async function addStrategyConfigUsingPOST(
  body: API.StrategyConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/strategyConfig/addStrategyConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除实时脱敏配置 POST /api/strategyConfig/batchDelete */
export async function batchDeleteUsingPOST2(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/strategyConfig/batchDelete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除实时脱敏配置 DELETE /api/strategyConfig/delStrategyConfig/${param0} */
export async function delStrategyConfigUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delStrategyConfigUsingDELETEParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/strategyConfig/delStrategyConfig/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 开启/关闭实时脱敏配置 POST /api/strategyConfig/exec */
export async function execStrategyConfigUsingPOST(
  body: API.StrategyConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/strategyConfig/exec', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询实时脱敏配置 GET /api/strategyConfig/getAll */
export async function getAllUsingGET8(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET8Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/strategyConfig/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改实时脱敏配置 PUT /api/strategyConfig/modifyStrategyConfig */
export async function modifyStrategyConfigUsingPUT(
  body: API.StrategyConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/strategyConfig/modifyStrategyConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
