// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取所有下载信息 GET /api/maskDownload/getAll */
export async function getAllUsingGET6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET6Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskDownload/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 数据溯源 GET /api/maskDownload/traceData */
export async function traceDataUsingGET(body: string, options?: { [key: string]: any }) {
  return request<API.Result>('/api/maskDownload/traceData', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
