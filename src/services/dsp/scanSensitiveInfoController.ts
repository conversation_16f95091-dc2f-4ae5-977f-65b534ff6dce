// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 分页获取敏感信息识别规则 GET /api/scanSensitiveInfo/getAllScanSensitiveInfo */
export async function getAllScanSensitiveInfoUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllScanSensitiveInfoUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitiveInfo/getAllScanSensitiveInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新数据扫描详情信息 更新数据扫描详情信息 PUT /api/scanSensitiveInfo/updateScanSensitiveInfo */
export async function updateScanSensitiveInfoUsingPUT(
  body: API.ScanSensitiveInfo,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitiveInfo/updateScanSensitiveInfo', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查看字段数据 查看字段数据 GET /api/scanSensitiveInfo/viewData */
export async function viewDataUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.viewDataUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scanSensitiveInfo/viewData', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
