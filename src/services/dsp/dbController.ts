// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** addDb POST /api/db/add */
export async function addDbUsingPOST(body: API.DB, options?: { [key: string]: any }) {
  return request<API.Result>('/api/db/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** batchDelete POST /api/db/batchDelete */
export async function batchDeleteUsingPOST1(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/db/batchDelete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** dbTest POST /api/db/dbTest */
export async function dbTestUsingPOST(body: API.DB, options?: { [key: string]: any }) {
  return request<API.Result>('/api/db/dbTest', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** delDb DELETE /api/db/delDb/${param0} */
export async function delDbUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delDbUsingDELETEParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/db/delDb/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** fetchSchemaAndTables GET /api/db/fetchSchemaAndTables */
export async function fetchSchemaAndTablesUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fetchSchemaAndTablesUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/db/fetchSchemaAndTables', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** getAll GET /api/db/getAll */
export async function getAllUsingGET4(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET4Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/db/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** getDbType GET /api/db/getDbType */
export async function getDbTypeUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/db/getDbType', {
    method: 'GET',
    ...(options || {}),
  });
}

/** modifyDb PUT /api/db/modifyDb */
export async function modifyDbUsingPUT(body: API.DB, options?: { [key: string]: any }) {
  return request<API.Result>('/api/db/modifyDb', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** searchDbById GET /api/db/searchDbById */
export async function searchDbByIdUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.searchDbByIdUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/db/searchDbById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
