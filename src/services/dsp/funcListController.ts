// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加功能 POST /api/funcList/addFuncList */
export async function addFuncListUsingPOST(body: API.FuncList, options?: { [key: string]: any }) {
  return request<API.Result>('/api/funcList/addFuncList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除功能信息 DELETE /api/funcList/batchDelFuncList */
export async function batchDelFuncListUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/funcList/batchDelFuncList', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除功能信息 DELETE /api/funcList/delFuncList */
export async function delFuncListUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delFuncListUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/funcList/delFuncList', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取分页获取功能信息 GET /api/funcList/getAllFuncList */
export async function getAllFuncListUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllFuncListUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/funcList/getAllFuncList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取功能列表map GET /api/funcList/getFuncListMap */
export async function getFuncListMapUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/funcList/getFuncListMap', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改功能信息 POST /api/funcList/modifyFuncList */
export async function modifyFuncListUsingPOST(
  body: API.FuncList,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/funcList/modifyFuncList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
