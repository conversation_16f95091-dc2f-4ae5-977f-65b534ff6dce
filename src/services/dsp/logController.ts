// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取日志 GET /api/log/getAll */
export async function getAllUsingGET5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET5Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/log/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
