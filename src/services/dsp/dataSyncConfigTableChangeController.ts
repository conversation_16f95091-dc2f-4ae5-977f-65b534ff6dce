// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取数据同步表修改信息 GET /api/dataSyncConfigTableChange/getDataSyncConfigTableChangeInfo */
export async function getDataSyncConfigTableChangeInfoUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDataSyncConfigTableChangeInfoUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfigTableChange/getDataSyncConfigTableChangeInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** getDataSyncConfigTableChangeInfoByFilter GET /api/dataSyncConfigTableChange/getDataSyncConfigTableChangeInfoPage */
export async function getDataSyncConfigTableChangeInfoByFilterUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDataSyncConfigTableChangeInfoByFilterUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>(
    '/api/dataSyncConfigTableChange/getDataSyncConfigTableChangeInfoPage',
    {
      method: 'GET',
      params: {
        ...params,
        filter: undefined,
        ...params['filter'],
      },
      ...(options || {}),
    },
  );
}
