// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加同步配置 POST /api/sync/addConfig */
export async function addUsingPOST3(body: API.SyncConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/sync/addConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除同步配置 POST /api/sync/batchDel */
export async function batchDelUsingPOST1(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/sync/batchDel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 数据比对 POST /api/sync/compare */
export async function compareUsingPOST(body: API.SyncConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/sync/compare', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除同步配置 DELETE /api/sync/delConfig/${param0} */
export async function delConfigUsingDELETE3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delConfigUsingDELETE3Params,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/sync/delConfig/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改同步配置 PUT /api/sync/editConfig */
export async function modifyConfigUsingPUT1(
  body: API.SyncConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/sync/editConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 执行同步 POST /api/sync/exec */
export async function execSyncUsingPOST1(body: API.SyncConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/sync/exec', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取表下所有表 GET /api/sync/fetchColumns */
export async function fetchColumnsUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fetchColumnsUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/sync/fetchColumns', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有数据库schema GET /api/sync/fetchSchemas */
export async function fetchSchemasUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fetchSchemasUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/sync/fetchSchemas', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取schema下所有表 GET /api/sync/fetchTables */
export async function fetchTablesUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fetchTablesUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/sync/fetchTables', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有同步配置 GET /api/sync/getAll */
export async function getAllUsingGET9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET9Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/sync/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 数据比对结果获取 POST /api/sync/getCompareResult */
export async function getCompareResultUsingPOST(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCompareResultUsingPOSTParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/sync/getCompareResult', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修复配置 POST /api/sync/repairSync */
export async function repairSyncUsingPOST(body: API.SyncConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/sync/repairSync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查看执行日志 POST /api/sync/syncInfo */
export async function syncInfoUsingPOST(body: API.SyncConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/sync/syncInfo', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
