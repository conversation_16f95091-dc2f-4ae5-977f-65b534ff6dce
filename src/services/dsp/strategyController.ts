// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 分页获取策略 GET /api/strategy/getAll */
export async function getAllStrategyUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllStrategyUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/strategy/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有可用策略 GET /api/strategy/getAllAble */
export async function getAbleStrategyUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/strategy/getAllAble', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 修改策略状态 POST /api/strategy/modifyStatus */
export async function modifyStatusUsingPOST(body: API.Strategy, options?: { [key: string]: any }) {
  return request<API.Result>('/api/strategy/modifyStatus', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
