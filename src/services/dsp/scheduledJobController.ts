// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** addSchedule POST /api/scheduledJob/addSchedule */
export async function addScheduleUsingPOST(
  body: API.ScheduledJob,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scheduledJob/addSchedule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** delSchedule DELETE /api/scheduledJob/delSchedule */
export async function delScheduleUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delScheduleUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scheduledJob/delSchedule', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** getAll GET /api/scheduledJob/getAll */
export async function getAllUsingGET7(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET7Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scheduledJob/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** startSchedule POST /api/scheduledJob/startSchedule */
export async function startScheduleUsingPOST(
  body: API.ScheduledJob,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scheduledJob/startSchedule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** stopSchedule POST /api/scheduledJob/stopSchedule */
export async function stopScheduleUsingPOST(
  body: API.ScheduledJob,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/scheduledJob/stopSchedule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** update POST /api/scheduledJob/update */
export async function updateUsingPOST(body: API.ScheduledJob, options?: { [key: string]: any }) {
  return request<API.Result>('/api/scheduledJob/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
