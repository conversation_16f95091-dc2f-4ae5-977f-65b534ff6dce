// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** addEmails POST /api/emails/savemails */
export async function addEmailsUsingPOST(body: API.Emails, options?: { [key: string]: any }) {
  return request<API.Result>('/api/emails/savemails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** testemails POST /api/emails/testemails */
export async function testemailsUsingPOST(body: API.Emails, options?: { [key: string]: any }) {
  return request<API.Result>('/api/emails/testemails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
