// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加接口 POST /api/permissions/addPermissions */
export async function addPermissionsUsingPOST(
  body: API.Permissions,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/permissions/addPermissions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除接口信息 DELETE /api/permissions/batchDelPermissions */
export async function batchDelPermissionsUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/permissions/batchDelPermissions', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除接口信息 DELETE /api/permissions/delPermissions */
export async function delPermissionsUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delPermissionsUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/permissions/delPermissions', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取分页获取接口信息 GET /api/permissions/getAllPermissions */
export async function getAllPermissionsUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllPermissionsUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/permissions/getAllPermissions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改接口信息 POST /api/permissions/modifyPermissions */
export async function modifyPermissionsUsingPOST(
  body: API.Permissions,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/permissions/modifyPermissions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 刷新接口信息 GET /api/permissions/scanPermissions */
export async function scanPermissionsUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/permissions/scanPermissions', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 更新接口信息 POST /api/permissions/updatePermissions */
export async function updatePermissionsUsingPOST(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/permissions/updatePermissions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
