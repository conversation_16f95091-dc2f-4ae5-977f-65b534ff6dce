// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加脱敏配置 POST /api/maskConfig/addMaskConfig */
export async function addMaskConfigUsingPOST(
  body: API.MaskConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/addMaskConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除敏感信息识别规则 DELETE /api/maskConfig/batchDelMaskConfig */
export async function batchDelMaskConfigUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/batchDelMaskConfig', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除敏感信息识别规则 DELETE /api/maskConfig/delMaskConfig */
export async function delMaskConfigUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delMaskConfigUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/delMaskConfig', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 执行脱敏配置 POST /api/maskConfig/executeMaskConfig */
export async function executeMaskConfigUsingPOST(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.executeMaskConfigUsingPOSTParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/executeMaskConfig', {
    method: 'POST',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取脱敏配置 GET /api/maskConfig/getAllMaskConfig */
export async function getAllMaskConfigUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllMaskConfigUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/getAllMaskConfig', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 条件分页查询脱敏配置 GET /api/maskConfig/getMaskConfig */
export async function getMaskConfigUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMaskConfigUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/getMaskConfig', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改脱敏配置 POST /api/maskConfig/modifyMaskConfig */
export async function modifyMaskConfigUsingPOST(
  body: API.MaskConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/modifyMaskConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 停止脱敏操作 POST /api/maskConfig/stopMaskByMaskConfigId */
export async function stopMaskByMaskConfigIdUsingPOST(
  body: API.MaskConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfig/stopMaskByMaskConfigId', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
