// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** getServers GET /api/serverConfig/getServers */
export async function getServersUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/serverConfig/getServers', {
    method: 'GET',
    ...(options || {}),
  });
}

/** addServers POST /api/serverConfig/saveServers */
export async function addServersUsingPOST(
  body: API.ServerConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/serverConfig/saveServers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** testLink GET /api/serverConfig/testLink */
export async function testLinkUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.testLinkUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/serverConfig/testLink', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
