// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加数据采集配置 添加配置 POST /api/dataCollection/addConfig */
export async function addUsingPOST(body: API.DataCollection, options?: { [key: string]: any }) {
  return request<API.Result>('/api/dataCollection/addConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除数据采集配置 DELETE /api/dataCollection/batchDel */
export async function batchDelUsingDELETE(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/dataCollection/batchDel', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除数据采集配置 删除配置 DELETE /api/dataCollection/delConfig/${param0} */
export async function delConfigUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delConfigUsingDELETEParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/dataCollection/delConfig/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 获取所有数据采集配置 GET /api/dataCollection/getAll */
export async function getAllUsingGET1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET1Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataCollection/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改数据采集配置 PUT /api/dataCollection/modifyConfig */
export async function editUsingPUT(body: API.DataCollection, options?: { [key: string]: any }) {
  return request<API.Result>('/api/dataCollection/modifyConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** testLink POST /api/dataCollection/testLink */
export async function testLinkUsingPOST(
  body: API.DataCollection,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataCollection/testLink', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
