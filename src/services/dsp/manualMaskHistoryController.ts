// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 下载文件 GET /api/manualMaskHistory/downloadLocal */
export async function downloadLocalUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.downloadLocalUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.InputStreamResource>('/api/manualMaskHistory/downloadLocal', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有手动脱敏配置 GET /api/manualMaskHistory/getAllManualMask */
export async function getAllManualMaskUsingGET1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllManualMaskUsingGET1Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMaskHistory/getAllManualMask', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
