// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 提交审批 POST /api/approvalList/addApprovalList */
export async function addApprovalListUsingPOST(
  body: API.ApprovalList,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/approvalList/addApprovalList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除审批信息 DELETE /api/approvalList/batchDelApprovalList */
export async function batchDelApprovalListUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/approvalList/batchDelApprovalList', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页获取审批信息 GET /api/approvalList/getAllApprovalList */
export async function getAllApprovalListUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllApprovalListUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/approvalList/getAllApprovalList', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改审批信息 POST /api/approvalList/modifyApprovalList */
export async function modifyApprovalListUsingPOST(
  body: API.ApprovalList,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/approvalList/modifyApprovalList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
