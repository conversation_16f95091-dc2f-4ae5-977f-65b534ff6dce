// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加审计配置 POST /api/auditConfig/add */
export async function addAuditUsingPOST(body: API.AuditConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/auditConfig/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除审计配置 POST /api/auditConfig/batchDelete */
export async function batchDeleteUsingPOST(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/auditConfig/batchDelete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除审计配置 DELETE /api/auditConfig/delAudit/${param0} */
export async function delAuditUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delAuditUsingDELETEParams,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/auditConfig/delAudit/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 开启/关闭审计配置 POST /api/auditConfig/exec */
export async function execAuditUsingPOST(body: API.AuditConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/auditConfig/exec', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取所有审计配置 GET /api/auditConfig/getAll */
export async function getAllUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/auditConfig/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改审计配置 PUT /api/auditConfig/modifyAuditConfig */
export async function modifyAuditConfigUsingPUT(
  body: API.AuditConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/auditConfig/modifyAuditConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
