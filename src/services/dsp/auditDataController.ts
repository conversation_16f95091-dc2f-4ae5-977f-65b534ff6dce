// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 获取所有审核数据 POST /api/auditData/getAll */
export async function getAllUsingPOST(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingPOSTParams,
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/auditData/getAll', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取审核数据详情 POST /api/auditData/getDetail */
export async function getDetailUsingPOST(body: API.AuditData, options?: { [key: string]: any }) {
  return request<API.Result>('/api/auditData/getDetail', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
