// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** addAlarm POST /api/alarm/addAlarm */
export async function addAlarmUsingPOST(body: API.Alarm, options?: { [key: string]: any }) {
  return request<API.Result>('/api/alarm/addAlarm', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** linkUser GET /api/alarm/getLinkUser */
export async function linkUserUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/alarm/getLinkUser', {
    method: 'GET',
    ...(options || {}),
  });
}

/** findUserName GET /api/alarm/searchLinkName */
export async function findUserNameUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/alarm/searchLinkName', {
    method: 'GET',
    ...(options || {}),
  });
}
