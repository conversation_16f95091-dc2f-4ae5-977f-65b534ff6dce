// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 导出日志 GET /api/dataSyncConfigInfo/exportLog */
export async function exportLogUsingGET(options?: { [key: string]: any }) {
  return request<API.InputStreamResource>('/api/dataSyncConfigInfo/exportLog', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取同步配置详情信息 GET /api/dataSyncConfigInfo/getDataSyncConfigInfo */
export async function getDataSyncConfigInfoUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getDataSyncConfigInfoUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfigInfo/getDataSyncConfigInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取kafka数据 GET /api/dataSyncConfigInfo/getKafkaContents */
export async function getKafkaContentsUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getKafkaContentsUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfigInfo/getKafkaContents', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取同步标配主键json文件 获取同步标配主键json文件 GET /api/dataSyncConfigInfo/getSyncConfig */
export async function getSyncConfigUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/dataSyncConfigInfo/getSyncConfig', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取topic分区 GET /api/dataSyncConfigInfo/getTopicPartitions */
export async function getTopicPartitionsUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getTopicPartitionsUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfigInfo/getTopicPartitions', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新同步配置主键json文件 更新同步配置主键json文件 PUT /api/dataSyncConfigInfo/updateSyncConfig */
export async function updateSyncConfigUsingPUT(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: API.updateSyncConfigUsingPUTParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfigInfo/updateSyncConfig', {
    method: 'PUT',
    data: body,
    ...(options || {}),
  });
}
