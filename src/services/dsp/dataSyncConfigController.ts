// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加同步配置 POST /api/dataSyncConfig/addConfig */
export async function addUsingPOST2(body: API.DataSyncConfig, options?: { [key: string]: any }) {
  return request<API.Result>('/api/dataSyncConfig/addConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除同步配置 POST /api/dataSyncConfig/batchDel */
export async function batchDelUsingPOST(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/dataSyncConfig/batchDel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除同步配置 DELETE /api/dataSyncConfig/delConfig/${param0} */
export async function delConfigUsingDELETE2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delConfigUsingDELETE2Params,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/dataSyncConfig/delConfig/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 执行同步配置 GET /api/dataSyncConfig/executeDataSync */
export async function executeDataSyncUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.executeDataSyncUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfig/executeDataSync', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有同步配置 GET /api/dataSyncConfig/getAll */
export async function getAllUsingGET3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET3Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfig/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改同步配置 PUT /api/dataSyncConfig/modifyConfig */
export async function modifyConfigUsingPUT(
  body: API.DataSyncConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfig/modifyConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 停止脱敏操作 POST /api/dataSyncConfig/stopSyncConfigId */
export async function stopSyncConfigIdUsingPOST(
  body: API.DataSyncConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/dataSyncConfig/stopSyncConfigId', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
