// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加手动脱敏配置 POST /api/manualMask/addManualMask */
export async function addManualMaskUsingPOST(
  body: API.ManualMaskConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMask/addManualMask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除手动脱敏配置 DELETE /api/manualMask/batchDelManualMask */
export async function batchDelManualMaskUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMask/batchDelManualMask', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除手动脱敏配置 DELETE /api/manualMask/delManualMask */
export async function delManualMaskUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delManualMaskUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMask/delManualMask', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 执行手动脱敏配置 POST /api/manualMask/executeManualMask */
export async function executeManualMaskUsingPOST(
  body: API.ManualMaskConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMask/executeManualMask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取所有手动脱敏配置 GET /api/manualMask/getAllManualMask */
export async function getAllManualMaskUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllManualMaskUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMask/getAllManualMask', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改手动脱敏配置 POST /api/manualMask/modifyManualMask */
export async function modifyManualMaskUsingPOST(
  body: API.ManualMaskConfig,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMask/modifyManualMask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 提交 POST /api/manualMask/submitSQL */
export async function submitSqlUsingPOST(
  body: Record<string, any>,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/manualMask/submitSQL', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 上传文件 POST /api/manualMask/uploadFile */
export async function uploadFileUsingPOST(body: string, options?: { [key: string]: any }) {
  return request<API.Result>('/api/manualMask/uploadFile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
