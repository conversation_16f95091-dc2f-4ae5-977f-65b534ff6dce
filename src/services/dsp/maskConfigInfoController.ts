// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 下载文件 GET /api/maskConfigInfo/fileDownload */
export async function fileDownloadUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.fileDownloadUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.InputStreamResource>('/api/maskConfigInfo/fileDownload', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有手动脱敏配置 GET /api/maskConfigInfo/getAllMaskConfigInfo */
export async function getAllMaskConfigInfoUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllMaskConfigInfoUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskConfigInfo/getAllMaskConfigInfo', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
