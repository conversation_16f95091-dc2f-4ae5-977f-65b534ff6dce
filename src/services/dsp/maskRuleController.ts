// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 批量保存脱敏策略配置 DELETE /api/maskRule/delBatchMaskRules */
export async function delBatchMaskRulesUsingDELETE(
  body: number[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskRule/delBatchMaskRules', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询分页脱敏策略配置 GET /api/maskRule/getMaskRule */
export async function getMaskRuleUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getMaskRuleUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskRule/getMaskRule', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 批量保存脱敏策略配置 POST /api/maskRule/saveBatchMaskRules */
export async function saveBatchMaskRulesUsingPOST(
  body: API.MaskRuleVo[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/maskRule/saveBatchMaskRules', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
