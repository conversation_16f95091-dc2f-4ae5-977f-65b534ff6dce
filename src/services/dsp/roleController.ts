// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加角色 POST /api/role/addRole */
export async function addRoleUsingPOST(body: API.Role, options?: { [key: string]: any }) {
  return request<API.Result>('/api/role/addRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除角色信息 DELETE /api/role/batchDelRole */
export async function batchDelRoleUsingDELETE(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/role/batchDelRole', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除角色信息 DELETE /api/role/delRole */
export async function delRoleUsingDELETE(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delRoleUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/role/delRole', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页获取分页获取角色信息 GET /api/role/getAllRole */
export async function getAllRoleUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllRoleUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/role/getAllRole', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 通过ID获取角色信息 GET /api/role/getRoleById */
export async function getRoleByIdUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getRoleByIdUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/role/getRoleById', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 修改角色信息 POST /api/role/modifyRole */
export async function modifyRoleUsingPOST(body: API.Role, options?: { [key: string]: any }) {
  return request<API.Result>('/api/role/modifyRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
