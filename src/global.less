:root {
  --primary-color: #13c2c2; // 修改主题色
  --primary-hover-color: #36cfc9; // 修改主题色
  --cyan-5: #36cfc9;
}

@font-face {
  font-weight: 700;
  font-family: '阿里妈妈数黑体 Bold';
  src: url('/fonts/noL0FY2kynRv6UZONbDeH.woff2') format('woff2'),
    url('/fonts/noL0FY2kynRv6UZONbDeH.woff') format('woff');
  font-display: swap;
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

.rklink-container {
  background-color: #fff;
}

.rklink-title {
  display: block;
  margin-bottom: 16px;
  color: @colorText;
  font-weight: 600;
}

.rklink-des-title {
  flex: auto;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// login
.ant-pro-form-login-container {
  flex: none;
  justify-content: center;

  .ant-pro-form-login-header {
    margin-bottom: 48px;
  }
}

// 布局
.ant-layout-content.ant-pro-layout-content .ant-pro-page-container {
  min-height: calc(100vh - 124px);
}

.ant-pro-global-footer {
  margin: 24px;
}

.ant-breadcrumb {
  li {
    a {
      &:hover {
        background-color: transparent;
      }
    }

    &:last-child a {
      color: @colorText;
    }
  }
}

.ant-pro-page-container-children-content {
  padding-top: 24px !important;
}

.ant-page-header {
  background-color: #fff;
}

.ant-pro-layout-has-footer {
  margin-block-start: 0 !important;
}

.ant-typography .ant-typography-copy {
  color: var(--primary-color);

  &:not(:disabled):hover {
    color: var(--primary-hover-color);
  }
}

.ant-btn-link {
  color: var(--primary-color);

  &:not(:disabled):hover {
    color: var(--primary-hover-color);
  }
}

// 在使用浏览器保存的数据时 输入框的样式
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  // 背景颜色
  background-color: transparent !important;
  // 背景图片
  background-image: none !important;
  //设置input输入框的背景颜色为透明色
  -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
  transition: background-color 50000s ease-in-out 0s;
}

// modal

.ant-modal {
  .ant-modal-header {
    margin-bottom: 24px;
  }

  .ant-modal-confirm-content {
    margin-block-start: 24px !important;
    margin-inline-start: 24px !important;
  }
}

.rklink-form-box {
  padding: 24px 24px 0 24px;
  overflow: hidden;
  background-color: #fff;
}

a {
  color: var(--primary-color);

  &:hover {
    color: var(--primary-hover-color);
  }
}

.ant-modal-confirm-btns {
  .ant-btn-primary:not(:disabled) {
    background-color: var(--primary-color);

    &:hover {
      background-color: var(--primary-hover-color);
    }

    &:active {
      color: #fff;
      background-color: #08979c;
    }
  }

  .ant-btn-default:not(:disabled):hover {
    color: var(--cyan-5);
    border-color: var(--cyan-5);
  }
}

.ant-modal-content {
  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    border-color: var(--cyan-5);
  }

  .ant-input-affix-wrapper:focus,
  .ant-input-affix-wrapper-focused {
    border-color: var(--cyan-5);
  }
}

.ant-layout-content.ant-pro-layout-content .ant-pro-page-container.detail-container {
  min-height: calc(100vh - 190px);

  .ant-form {
    padding: 16px 24px;
    background-color: #fff;
  }
}

//表格中的禁用启用按钮
.ant-btn-link-table {
  margin: 0;
  padding: 0;
}

:where(.css-dev-only-do-not-override-a2qpqq).ant-timeline .ant-timeline-item-head-custom {
  background-color: unset;
}

.ant-timeline-item-label {
  margin: 10px -180px;
  font-size: 16px;
}
